{"models": ["from django.db import models\n\nclass WOCategory(models.Model):\n    # Maps to the existing 'Id' primary key column in 'tblSD_WO_Category'\n    id = models.IntegerField(db_column='Id', primary_key=True) \n    \n    # Maps to the 'Category' column\n    category = models.Char<PERSON>ield(db_column='Category', max_length=255) \n    \n    # Maps to the 'SubCategory' column, used for 'Symbol' in the UI\n    # Enforces max_length=1 as observed in the ASP.NET UI's MaxLength=\"1\"\n    symbol = models.CharField(db_column='SubCategory', max_length=1) \n\n    class Meta:\n        managed = False  # Important: Tells Django not to manage this table's schema\n        db_table = 'tblSD_WO_Category'  # The exact name of your existing database table\n        verbose_name = 'Work Order Category'\n        verbose_name_plural = 'Work Order Categories'\n\n    def __str__(self):\n        # Provides a human-readable representation of the object\n        return f\"{self.category} ({self.symbol})\"\n\n    # --- Business Logic Methods (Fat Model Principle) ---\n    # Any complex validation beyond basic field requirements,\n    # or operations involving multiple fields/other models, go here.\n\n    def is_unique_category(self, exclude_id=None):\n        \"\"\"Checks if the category name is unique (case-insensitive).\"\"\"\n        qs = WOCategory.objects.filter(category__iexact=self.category)\n        if exclude_id:\n            qs = qs.exclude(id=exclude_id)\n        return not qs.exists()\n\n    def clean(self):\n        \"\"\"\n        Custom cleaning/validation for the model.\n        Called by form.is_valid() if using a ModelForm.\n        \"\"\"\n        from django.core.exceptions import ValidationError\n        if not self.is_unique_category(exclude_id=self.id):\n            raise ValidationError({'category': 'This category name already exists.'})\n        \n        # Ensure symbol is uppercase as per common convention for symbols\n        self.symbol = self.symbol.upper()"], "views": ["from django.views.generic import List<PERSON>ie<PERSON>, <PERSON><PERSON>View, UpdateView, DeleteView\nfrom django.urls import reverse_lazy\nfrom django.contrib import messages\nfrom django.http import HttpResponse\nfrom django.template.loader import render_to_string\nfrom .models import WOCategory\nfrom .forms import WOCategoryForm\n\n# Helper mixin for common HTMX form submission logic\nclass HtmxFormMixin:\n    def form_valid(self, form):\n        # Save the object (this calls the model's clean method implicitly before saving)\n        response = super().form_valid(form)\n        messages.success(self.request, f\"{self.model._meta.verbose_name} saved successfully.\")\n        \n        # If it's an HTMX request, return a 204 No Content to prevent full page reload\n        # and trigger a custom event for the list to refresh.\n        if self.request.headers.get('HX-Request'):\n            return HttpResponse(\n                status=204, # No Content\n                headers={\n                    'HX-Trigger': 'refreshWOCategoryList' # Custom HTMX event\n                }\n            )\n        return response\n\n    def form_invalid(self, form):\n        # If form is invalid and it's an HTMX request, re-render the form with errors\n        # and return it in the response.\n        if self.request.headers.get('HX-Request'):\n            return HttpResponse(\n                render_to_string(self.template_name, {'form': form}, self.request),\n                status=200, # OK, re-rendering content\n            )\n        return super().form_invalid(form) # Default behavior for non-HTMX requests\n\nclass WOCategoryListView(ListView):\n    \"\"\"Displays a list of all Work Order Categories.\"\"\"\n    model = WOCategory\n    template_name = 'workorders/wocategory/list.html'\n    context_object_name = 'wocategories' # How the list is accessed in the template\n\nclass WOCategoryTablePartialView(ListView):\n    \"\"\"Returns only the HTML table content for HTMX partial updates.\"\"\"\n    model = WOCategory\n    template_name = 'workorders/wocategory/_wocategory_table.html'\n    context_object_name = 'wocategories'\n\n    def get_queryset(self):\n        # Order the categories by name for consistent display\n        return super().get_queryset().order_by('category')\n\nclass WOCategoryCreateView(HtmxFormMixin, CreateView):\n    \"\"\"Handles creation of new Work Order Categories.\"\"\"\n    model = WOCategory\n    form_class = WOCategoryForm\n    template_name = 'workorders/wocategory/_wocategory_form.html' # This is a partial template\n    success_url = reverse_lazy('wocategory_list') # Fallback URL if not HTMX\n\nclass WOCategoryUpdateView(HtmxFormMixin, UpdateView):\n    \"\"\"Handles updating existing Work Order Categories.\"\"\"\n    model = WOCategory\n    form_class = WOCategoryForm\n    template_name = 'workorders/wocategory/_wocategory_form.html' # This is a partial template\n    success_url = reverse_lazy('wocategory_list') # Fallback URL if not HTMX\n\nclass WOCategoryDeleteView(DeleteView):\n    \"\"\"Handles deletion of Work Order Categories.\"\"\"\n    model = WOCategory\n    template_name = 'workorders/wocategory/_wocategory_confirm_delete.html' # This is a partial template\n    success_url = reverse_lazy('wocategory_list') # Fallback URL if not HTMX\n\n    def delete(self, request, *args, **kwargs):\n        # Get the object before deletion for messaging\n        obj = self.get_object()\n        response = super().delete(request, *args, **kwargs)\n        messages.success(self.request, f\"{obj._meta.verbose_name} '{obj}' deleted successfully.\")\n        \n        # If it's an HTMX request, return a 204 No Content\n        # and trigger a custom event for the list to refresh.\n        if request.headers.get('HX-Request'):\n            return HttpResponse(\n                status=204, # No Content\n                headers={\n                    'HX-Trigger': 'refreshWOCategoryList'\n                }\n            )\n        return response"], "forms": ["from django import forms\nfrom .models import WOCategory\n\nclass WOCategoryForm(forms.ModelForm):\n    class Meta:\n        model = WOCategory\n        fields = ['category', 'symbol'] # 'id' is a primary key and auto-managed, so not exposed in form for direct user input\n        widgets = {\n            'category': forms.TextInput(attrs={\n                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',\n                'placeholder': 'Enter category name'\n            }),\n            'symbol': forms.TextInput(attrs={\n                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',\n                'maxlength': '1', # Enforces the single-character constraint from ASP.NET UI\n                'placeholder': 'Enter 1-char symbol'\n            }),\n        }\n        labels = {\n            'category': 'Category Name',\n            'symbol': 'Symbol',\n        }\n        \n    def clean(self):\n        \"\"\"\n        Custom form-level validation.\n        Leverages the model's clean method for business logic.\n        \"\"\"\n        cleaned_data = super().clean()\n        instance = self.instance # The model instance being edited (or None for new)\n        \n        # Manually call model's clean method to trigger model-level validation (e.g., uniqueness)\n        try:\n            # Create a temporary instance to validate if the form is for creation\n            # or if the instance is not yet saved.\n            temp_instance = WOCategory(\n                id=instance.id if instance and instance.id else None, # Pass existing ID if editing\n                category=cleaned_data.get('category'),\n                symbol=cleaned_data.get('symbol')\n            )\n            temp_instance.clean()\n            # If successful, assign back the cleaned symbol (e.g., uppercase)\n            cleaned_data['symbol'] = temp_instance.symbol\n        except forms.ValidationError as e:\n            # Re-raise form errors with the correct field mapping\n            self.add_error(e.error_dict.keys().__iter__().__next__(), e.message)\n            \n        return cleaned_data"], "urls": ["from django.urls import path\nfrom .views import (\n    WOCategoryListView,\n    WOCategoryTablePartialView,\n    WOCategoryCreateView,\n    WOCategoryUpdateView,\n    WOCategoryDeleteView\n)\n\nurlpatterns = [\n    # Main page to display all categories\n    path('wo-categories/', WOCategoryListView.as_view(), name='wocategory_list'),\n    \n    # HTMX endpoint to refresh only the table content\n    path('wo-categories/table/', WOCategoryTablePartialView.as_view(), name='wocategory_table'), \n    \n    # HTMX endpoint to get and post the add form (into a modal)\n    path('wo-categories/add/', WOCategoryCreateView.as_view(), name='wocategory_add'),\n    \n    # HTMX endpoint to get and post the edit form for a specific category (into a modal)\n    path('wo-categories/edit/<int:pk>/', WOCategoryUpdateView.as_view(), name='wocategory_edit'),\n    \n    # HTMX endpoint to get and post the delete confirmation for a specific category (into a modal)\n    path('wo-categories/delete/<int:pk>/', WOCategoryDeleteView.as_view(), name='wocategory_delete'),\n]"], "templates": ["{% extends 'core/base.html' %} {# Assumes core/base.html provides the base layout, HTMX, Alpine.js, and DataTables CDNs #}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <div class=\"flex justify-between items-center mb-6\">\n        <h2 class=\"text-3xl font-extrabold text-gray-800\">Work Order Categories</h2>\n        <button \n            class=\"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-lg transform transition duration-150 ease-in-out hover:scale-105\"\n            hx-get=\"{% url 'wocategory_add' %}\" {# HTMX GET request to fetch the add form #}\n            hx-target=\"#modalContent\" {# Target the modal content area #}\n            hx-trigger=\"click\" {# On button click #}\n            _=\"on click add .is-active to #modal\"> {# Alpine.js/Hyperscript to show the modal #}\n            Add New Category\n        </button>\n    </div>\n    \n    {# Container for the category table, which will be loaded/refreshed via HTMX #}\n    <div id=\"wocategoryTable-container\"\n         hx-trigger=\"load, refreshWOCategoryList from:body\" {# Load on page load, or when 'refreshWOCategoryList' event is triggered #}\n         hx-get=\"{% url 'wocategory_table' %}\" {# URL to fetch the table partial #}\n         hx-swap=\"innerHTML\" {# Replace the inner HTML of this div #}>\n        <!-- Initial loading state -->\n        <div class=\"flex justify-center items-center h-48 bg-white rounded-lg shadow-md\">\n            <div class=\"inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500\"></div>\n            <p class=\"ml-4 text-lg text-gray-600\">Loading categories...</p>\n        </div>\n    </div>\n    \n    <!-- Modal for form/delete confirmation (hidden by default) -->\n    <div id=\"modal\" class=\"fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 hidden\"\n         _=\"on click if event.target.id == 'modal' remove .is-active from me\"> {# Click outside to close modal #}\n        <div id=\"modalContent\" class=\"bg-white p-6 rounded-lg shadow-xl max-w-lg w-full relative transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full\">\n            {# Form or confirmation content will be loaded here via HTMX #}\n        </div>\n    </div>\n</div>\n{% endblock %}\n\n{% block extra_js %}\n<script>\n    // Alpine.js integration for modal visibility management\n    document.addEventListener('alpine:init', () => {\n        // Listen for htmx afterSettle event to show modal after content is loaded\n        document.body.addEventListener('htmx:afterSettle', (event) => {\n            // Check if the target was modalContent and the request was successful\n            if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 200) {\n                document.getElementById('modal').classList.add('is-active');\n            }\n        });\n\n        // Listen for htmx:afterRequest on forms to hide modal after successful submission (204 No Content)\n        document.body.addEventListener('htmx:afterRequest', (event) => {\n            const form = event.detail.target;\n            if (form.tagName === 'FORM' && event.detail.xhr.status === 204) {\n                document.getElementById('modal').classList.remove('is-active');\n            }\n        });\n\n        // Close modal on escape key press\n        document.addEventListener('keydown', (event) => {\n            if (event.key === 'Escape') {\n                document.getElementById('modal').classList.remove('is-active');\n            }\n        });\n    });\n</script>\n{% endblock %}", "<div class=\"bg-white shadow-md rounded-lg overflow-hidden p-4\">\n    <table id=\"wocategoryTable\" class=\"min-w-full divide-y divide-gray-200\">\n        <thead class=\"bg-gray-50\">\n            <tr>\n                <th class=\"py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">SN</th>\n                <th class=\"py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Category Name</th>\n                <th class=\"py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Symbol</th>\n                <th class=\"py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n            </tr>\n        </thead>\n        <tbody class=\"bg-white divide-y divide-gray-200\">\n            {% for obj in wocategories %}\n            <tr class=\"hover:bg-gray-50\">\n                <td class=\"py-3 px-4 whitespace-nowrap text-sm text-gray-500\">{{ forloop.counter }}</td>\n                <td class=\"py-3 px-4 whitespace-nowrap text-sm text-gray-900\">{{ obj.category }}</td>\n                <td class=\"py-3 px-4 whitespace-nowrap text-sm text-gray-500\">{{ obj.symbol }}</td>\n                <td class=\"py-3 px-4 whitespace-nowrap text-sm font-medium\">\n                    <button \n                        class=\"bg-yellow-500 hover:bg-yellow-600 text-white py-1 px-3 rounded-md text-xs mr-2 shadow-sm transition duration-150 ease-in-out\"\n                        hx-get=\"{% url 'wocategory_edit' obj.pk %}\" {# HTMX GET request for edit form #}\n                        hx-target=\"#modalContent\" {# Target the modal #}\n                        hx-trigger=\"click\" {# On click #}\n                        _=\"on click add .is-active to #modal\"> {# Show modal #}\n                        Edit\n                    </button>\n                    <button \n                        class=\"bg-red-500 hover:bg-red-600 text-white py-1 px-3 rounded-md text-xs shadow-sm transition duration-150 ease-in-out\"\n                        hx-get=\"{% url 'wocategory_delete' obj.pk %}\" {# HTMX GET request for delete confirmation #}\n                        hx-target=\"#modalContent\" {# Target the modal #}\n                        hx-trigger=\"click\" {# On click #}\n                        _=\"on click add .is-active to #modal\"> {# Show modal #}\n                        Delete\n                    </button>\n                </td>\n            </tr>\n            {% empty %}\n            <tr>\n                <td colspan=\"4\" class=\"py-4 px-4 text-center text-gray-500\">\n                    No work order categories found. \n                    <button\n                        class=\"text-blue-600 hover:text-blue-800 font-semibold underline\"\n                        hx-get=\"{% url 'wocategory_add' %}\"\n                        hx-target=\"#modalContent\"\n                        hx-trigger=\"click\"\n                        _=\"on click add .is-active to #modal\">\n                        Click here to add one.\n                    </button>\n                </td>\n            </tr>\n            {% endfor %}\n        </tbody>\n    </table>\n</div>\n\n<script>\n    // Initialize DataTables on the table after HTMX swaps the content.\n    // It's crucial to destroy any existing DataTable instance before re-initialization.\n    $(document).ready(function() {\n        if ($.fn.DataTable.isDataTable('#wocategoryTable')) {\n            $('#wocategoryTable').DataTable().destroy();\n        }\n        $('#wocategoryTable').DataTable({\n            \"paging\": true,\n            \"lengthChange\": true,\n            \"searching\": true,\n            \"ordering\": true,\n            \"info\": true,\n            \"autoWidth\": false,\n            \"responsive\": true,\n            \"pageLength\": 10, // Default page length\n            \"lengthMenu\": [[10, 25, 50, -1], [10, 25, 50, \"All\"]], // Options for page length\n        });\n    });\n</script>", "<div class=\"p-6\">\n    <h3 class=\"text-xl font-semibold text-gray-900 mb-5 border-b pb-3\">\n        {{ form.instance.pk|yesno:'Edit,Add' }} Work Order Category\n    </h3>\n    <form hx-post=\"{{ request.path }}\" hx-swap=\"none\" class=\"space-y-6\"> {# hx-swap=\"none\" to prevent modal closing on invalid submission #}\n        {% csrf_token %} {# Django's CSRF token for security #}\n        \n        <div class=\"mb-4\">\n            <label for=\"{{ form.category.id_for_label }}\" class=\"block text-sm font-medium text-gray-700\">\n                Category Name <span class=\"text-red-500\">*</span>\n            </label>\n            {{ form.category }} {# Renders the Django form field #}\n            {% if form.category.errors %}\n            <p class=\"text-red-500 text-xs mt-1\">{{ form.category.errors }}</p>\n            {% endif %}\n        </div>\n\n        <div class=\"mb-6\">\n            <label for=\"{{ form.symbol.id_for_label }}\" class=\"block text-sm font-medium text-gray-700\">\n                Symbol <span class=\"text-red-500\">*</span>\n            </label>\n            {{ form.symbol }} {# Renders the Django form field #}\n            {% if form.symbol.errors %}\n            <p class=\"text-red-500 text-xs mt-1\">{{ form.symbol.errors }}</p>\n            {% endif %}\n        </div>\n        \n        <div class=\"flex justify-end space-x-3 pt-4 border-t\">\n            <button \n                type=\"button\" \n                class=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out\"\n                _=\"on click remove .is-active from #modal\"> {# Closes the modal using Hyperscript #}\n                Cancel\n            </button>\n            <button \n                type=\"submit\" \n                class=\"bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out\">\n                Save Category\n            </button>\n        </div>\n    </form>\n</div>", "<div class=\"p-6\">\n    <h3 class=\"text-xl font-semibold text-gray-900 mb-5 border-b pb-3\">Confirm Deletion</h3>\n    <p class=\"text-gray-700 mb-6\">Are you sure you want to delete the category: <strong>\"{{ wocategory.category }} ({{ wocategory.symbol }})\"</strong>? This action cannot be undone.</p>\n\n    <form hx-post=\"{{ request.path }}\" hx-swap=\"none\"> {# hx-swap=\"none\" for successful deletion #}\n        {% csrf_token %}\n        <div class=\"flex justify-end space-x-3 pt-4 border-t\">\n            <button \n                type=\"button\" \n                class=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out\"\n                _=\"on click remove .is-active from #modal\"> {# Closes the modal using Hyperscript #}\n                Cancel\n            </button>\n            <button \n                type=\"submit\" \n                class=\"bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out\">\n                Delete Category\n            </button>\n        </div>\n    </form>\n</div>"], "tests": ["from django.test import TestCase, Client\nfrom django.urls import reverse\nfrom .models import WOCategory\nfrom django.core.exceptions import ValidationError as DjangoValidationError\n\nclass WOCategoryModelTest(TestCase):\n    @classmethod\n    def setUpTestData(cls):\n        # Create initial test data for all model tests.\n        # Since managed=False, we're assuming the database table exists and IDs are managed by it.\n        # For testing, we ensure IDs are unique if we're explicitly setting them.\n        cls.category1 = WOCategory.objects.create(id=1, category='Engineering', symbol='E')\n        cls.category2 = WOCategory.objects.create(id=2, category='Operations', symbol='O')\n  \n    def test_wocategory_creation(self):\n        \"\"\"Test that a WOCategory object can be created and its attributes are correct.\"\"\"\n        obj = WOCategory.objects.get(id=self.category1.id)\n        self.assertEqual(obj.category, 'Engineering')\n        self.assertEqual(obj.symbol, 'E')\n        self.assertEqual(str(obj), 'Engineering (E)') # Test __str__ method\n\n    def test_category_verbose_name(self):\n        \"\"\"Test the verbose name for the model.\"\"\"\n        self.assertEqual(WOCategory._meta.verbose_name, 'Work Order Category')\n        self.assertEqual(WOCategory._meta.verbose_name_plural, 'Work Order Categories')\n        \n    def test_symbol_max_length_defined(self):\n        \"\"\"Verify the max_length for the 'symbol' field.\"\"\"\n        max_length = WOCategory._meta.get_field('symbol').max_length\n        self.assertEqual(max_length, 1)\n\n    def test_unique_category_validation(self):\n        \"\"\"Test model-level uniqueness validation for 'category' field.\"\"\"\n        # Attempt to create a category with an existing name (case-insensitive)\n        with self.assertRaisesMessage(DjangoValidationError, 'This category name already exists.'):\n            WOCategory(id=3, category='engineering', symbol='X').clean() # Will raise error via .clean()\n\n        # Test updating to a duplicate name\n        cat_to_update = WOCategory.objects.create(id=4, category='Design', symbol='D')\n        cat_to_update.category = 'Engineering' # Duplicate name\n        with self.assertRaisesMessage(DjangoValidationError, 'This category name already exists.'):\n            cat_to_update.clean() # Will raise error via .clean()\n\n    def test_unique_category_validation_self_exclude(self):\n        \"\"\"Test that an object can be saved with its own name without validation error.\"\"\"\n        # No error when updating self to same category name\n        self.category1.category = 'Engineering'\n        self.category1.clean() # Should not raise validation error\n\n    def test_symbol_uppercase_conversion(self):\n        \"\"\"Test that the symbol is converted to uppercase on clean/save.\"\"\"\n        new_cat = WOCategory(id=5, category='Testing', symbol='t')\n        new_cat.clean() # Calling clean() will convert 't' to 'T'\n        self.assertEqual(new_cat.symbol, 'T')\n        new_cat.save()\n        self.assertEqual(WOCategory.objects.get(id=5).symbol, 'T')\n\n\nclass WOCategoryViewsTest(TestCase):\n    @classmethod\n    def setUpTestData(cls):\n        # Create test data for all view tests.\n        cls.category_alpha = WOCategory.objects.create(id=101, category='Alpha', symbol='A')\n        cls.category_beta = WOCategory.objects.create(id=102, category='Beta', symbol='B')\n    \n    def setUp(self):\n        # Set up a new client for each test method to ensure isolated requests.\n        self.client = Client()\n    \n    def test_list_view(self):\n        \"\"\"Test the main list view loads correctly and displays data.\"\"\"\n        response = self.client.get(reverse('wocategory_list'))\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'workorders/wocategory/list.html')\n        self.assertIn('wocategories', response.context)\n        self.assertContains(response, 'Alpha')\n        self.assertContains(response, 'Beta')\n        \n    def test_table_partial_view_htmx(self):\n        \"\"\"Test the HTMX partial view for the table content.\"\"\"\n        headers = {'HTTP_HX_REQUEST': 'true'} # Mimic an HTMX request\n        response = self.client.get(reverse('wocategory_table'), **headers)\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'workorders/wocategory/_wocategory_table.html')\n        self.assertIn('wocategories', response.context)\n        self.assertContains(response, 'Alpha')\n        self.assertContains(response, 'Beta')\n        self.assertContains(response, '<table id=\"wocategoryTable\"') # Ensure DataTables table structure is present\n        \n    def test_create_view_get_htmx(self):\n        \"\"\"Test fetching the create form via HTMX.\"\"\"\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        response = self.client.get(reverse('wocategory_add'), **headers)\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'workorders/wocategory/_wocategory_form.html')\n        self.assertIn('form', response.context)\n        self.assertContains(response, 'Add Work Order Category')\n        \n    def test_create_view_post_htmx_success(self):\n        \"\"\"Test successful creation of a new category via HTMX.\"\"\"\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        data = {\n            'id': 103, # Provide a unique ID for the new object (important for managed=False)\n            'category': 'Gamma',\n            'symbol': 'G',\n        }\n        response = self.client.post(reverse('wocategory_add'), data, **headers)\n        \n        # On successful HTMX form submission, expect 204 No Content\n        self.assertEqual(response.status_code, 204)\n        self.assertTrue(WOCategory.objects.filter(category='Gamma').exists())\n        self.assertIn('HX-Trigger', response.headers)\n        self.assertEqual(response.headers['HX-Trigger'], 'refreshWOCategoryList')\n\n    def test_create_view_post_htmx_invalid(self):\n        \"\"\"Test creation with invalid data via HTMX.\"\"\"\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        data = {\n            'category': '', # Missing required field\n            'symbol': 'XY', # Invalid symbol (too long)\n        }\n        response = self.client.post(reverse('wocategory_add'), data, **headers)\n        \n        # Expect 200 OK, as the form is re-rendered with errors\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'workorders/wocategory/_wocategory_form.html')\n        self.assertContains(response, 'This field is required.')\n        self.assertContains(response, 'Ensure this value has at most 1 character (it has 2).') # Django's default CharField error for max_length\n        # Ensure category 'Gamma' was NOT created\n        self.assertFalse(WOCategory.objects.filter(category='Gamma').exists())\n\n    def test_create_view_post_htmx_duplicate_category(self):\n        \"\"\"Test creation with a duplicate category name via HTMX.\"\"\"\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        data = {\n            'id': 104, # Unique ID\n            'category': 'Alpha', # Duplicate category name\n            'symbol': 'X',\n        }\n        response = self.client.post(reverse('wocategory_add'), data, **headers)\n        \n        self.assertEqual(response.status_code, 200) # Form re-rendered with errors\n        self.assertTemplateUsed(response, 'workorders/wocategory/_wocategory_form.html')\n        self.assertContains(response, 'This category name already exists.')\n        \n    def test_update_view_get_htmx(self):\n        \"\"\"Test fetching the update form via HTMX.\"\"\"\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        response = self.client.get(reverse('wocategory_edit', args=[self.category_alpha.pk]), **headers)\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'workorders/wocategory/_wocategory_form.html')\n        self.assertIn('form', response.context)\n        self.assertEqual(response.context['form'].instance.category, 'Alpha')\n        \n    def test_update_view_post_htmx_success(self):\n        \"\"\"Test successful update of a category via HTMX.\"\"\"\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        data = {\n            'category': 'Alpha Updated',\n            'symbol': 'A',\n        }\n        response = self.client.post(reverse('wocategory_edit', args=[self.category_alpha.pk]), data, **headers)\n        \n        self.assertEqual(response.status_code, 204) # 204 No Content on success\n        self.category_alpha.refresh_from_db() # Reload object from DB to get updated values\n        self.assertEqual(self.category_alpha.category, 'Alpha Updated')\n        self.assertIn('HX-Trigger', response.headers)\n        self.assertEqual(response.headers['HX-Trigger'], 'refreshWOCategoryList')\n\n    def test_update_view_post_htmx_invalid(self):\n        \"\"\"Test update with invalid data via HTMX.\"\"\"\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        data = {\n            'category': 'Alpha Updated',\n            'symbol': '', # Missing required symbol\n        }\n        response = self.client.post(reverse('wocategory_edit', args=[self.category_alpha.pk]), data, **headers)\n        \n        self.assertEqual(response.status_code, 200) # Form re-rendered with errors\n        self.assertTemplateUsed(response, 'workorders/wocategory/_wocategory_form.html')\n        self.assertContains(response, 'This field is required.')\n        # Ensure category was NOT updated\n        self.category_alpha.refresh_from_db()\n        self.assertNotEqual(self.category_alpha.symbol, '')\n\n    def test_update_view_post_htmx_duplicate_category(self):\n        \"\"\"Test updating a category to a name that already exists (duplicate).\"\"\"\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        data = {\n            'category': 'Beta', # Category 'Beta' already exists\n            'symbol': 'Z',\n        }\n        response = self.client.post(reverse('wocategory_edit', args=[self.category_alpha.pk]), data, **headers)\n        \n        self.assertEqual(response.status_code, 200) # Form re-rendered with errors\n        self.assertTemplateUsed(response, 'workorders/wocategory/_wocategory_form.html')\n        self.assertContains(response, 'This category name already exists.')\n        # Ensure category was NOT updated\n        self.category_alpha.refresh_from_db()\n        self.assertNotEqual(self.category_alpha.category, 'Beta')\n\n    def test_delete_view_get_htmx(self):\n        \"\"\"Test fetching the delete confirmation via HTMX.\"\"\"\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        response = self.client.get(reverse('wocategory_delete', args=[self.category_beta.pk]), **headers)\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'workorders/wocategory/_wocategory_confirm_delete.html')\n        self.assertIn('wocategory', response.context)\n        self.assertEqual(response.context['wocategory'].category, 'Beta')\n        \n    def test_delete_view_post_htmx_success(self):\n        \"\"\"Test successful deletion of a category via HTMX.\"\"\"\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        # Ensure object exists before deletion attempt\n        self.assertTrue(WOCategory.objects.filter(pk=self.category_beta.pk).exists())\n        response = self.client.post(reverse('wocategory_delete', args=[self.category_beta.pk]), **headers)\n        \n        self.assertEqual(response.status_code, 204) # 204 No Content on success\n        self.assertFalse(WOCategory.objects.filter(pk=self.category_beta.pk).exists()) # Verify deletion\n        self.assertIn('HX-Trigger', response.headers)\n        self.assertEqual(response.headers['HX-Trigger'], 'refreshWOCategoryList')"], "admin": []}