{"models": ["from django.db import models\n\nclass WoCategory(models.Model):\n    \"\"\"\n    Represents a Work Order Category in the database.\n    Maps to the existing 'tbl_WO_Category' table.\n    \"\"\"\n    category_id = models.IntegerField(db_column='CategoryID', primary_key=True)\n    category_name = models.CharField(db_column='CategoryName', max_length=255, unique=True, verbose_name=\"Category Name\")\n    category_description = models.TextField(db_column='CategoryDescription', blank=True, null=True, verbose_name=\"Description\")\n    is_active = models.BooleanField(db_column='IsActive', default=True, verbose_name=\"Is Active\")\n    created_date = models.DateTimeField(db_column='CreatedDate', auto_now_add=True, verbose_name=\"Created Date\")\n\n    class Meta:\n        managed = False  # Tells Django not to manage this table's creation, deletion, etc.\n        db_table = 'tbl_WO_Category'\n        verbose_name = 'Work Order Category'\n        verbose_name_plural = 'Work Order Categories'\n        ordering = ['category_name'] # Default ordering for lists\n\n    def __str__(self):\n        \"\"\"String representation of the Work Order Category.\"\"\"\n        return self.category_name\n\n    def save(self, *args, **kwargs):\n        \"\"\"\n        Custom save method to potentially handle auto-population of CategoryID\n        if it's not an identity column in the database and needs Django to set it.\n        For identity columns, it's usually handled by the database.\n        \"\"\"\n        # Example: If CategoryID is NOT auto-incrementing in DB and you need to generate it\n        # if not self.category_id:\n        #     last_id = WoCategory.objects.all().order_by('-category_id').first()\n        #     self.category_id = (last_id.category_id + 1) if last_id else 1\n        super().save(*args, **kwargs)\n\n    def can_be_deleted(self):\n        \"\"\"\n        Business logic: Checks if this category can be deleted.\n        Example: A category cannot be deleted if it's linked to active work orders.\n        This method would query related tables.\n        \"\"\"\n        # Placeholder for actual business logic\n        # For demonstration, let's say categories created in the last 24 hours cannot be deleted.\n        # This would require linking to a 'WorkOrder' model, for example.\n        # if self.workorder_set.filter(status='active').exists(): # Assuming a reverse relation 'workorder_set'\n        #    return False\n        # return True\n        return True # For now, allow deletion always."], "views": ["from django.views.generic import ListView, <PERSON>reateView, UpdateView, DeleteView, View\nfrom django.urls import reverse_lazy\nfrom django.contrib import messages\nfrom django.http import HttpResponse, HttpResponseRedirect\nfrom .models import WoCategory\nfrom .forms import WoCategoryForm\nfrom django.template.loader import render_to_string\n\nclass WoCategoryListView(ListView):\n    \"\"\"\n    Displays the main dashboard page for Work Order Categories.\n    The actual table content is loaded via HTMX from WoCategoryTablePartialView.\n    \"\"\"\n    model = WoCategory\n    template_name = 'sales_distribution/wocategory/list.html'\n    context_object_name = 'wocategories' # This is only used if the main list template itself renders data.\n                                         # For HTMX setup, this view mainly provides the structural page.\n\nclass WoCategoryTablePartialView(ListView):\n    \"\"\"\n    Renders only the Work Order Categories table as a partial HTMX response.\n    Used to refresh the table after CRUD operations.\n    \"\"\"\n    model = WoCategory\n    template_name = 'sales_distribution/wocategory/_wocategory_table.html'\n    context_object_name = 'wocategories'\n    \n    def get_queryset(self):\n        \"\"\"\n        Ensures the list is ordered consistently.\n        \"\"\"\n        return super().get_queryset().order_by('category_name')\n\nclass WoCategoryCreateView(CreateView):\n    \"\"\"\n    Handles creation of new Work Order Categories.\n    Responds with HTMX triggers for dynamic updates.\n    \"\"\"\n    model = WoCategory\n    form_class = WoCategoryForm\n    template_name = 'sales_distribution/wocategory/_wocategory_form.html' # This is a partial template for modal\n    success_url = reverse_lazy('wocategory_list') # Fallback for non-HTMX requests\n\n    def form_valid(self, form):\n        response = super().form_valid(form)\n        messages.success(self.request, 'Work Order Category added successfully.')\n        if self.request.headers.get('HX-Request'):\n            # For HTMX requests, return a 204 No Content response\n            # and trigger a custom event to refresh the category list.\n            return HttpResponse(\n                status=204,\n                headers={\n                    'HX-Trigger': 'refreshWoCategoryList, showToastMessage'\n                }\n            )\n        return response # For non-HTMX requests, redirect\n\nclass WoCategoryUpdateView(UpdateView):\n    \"\"\"\n    Handles updating existing Work Order Categories.\n    Responds with HTMX triggers for dynamic updates.\n    \"\"\"\n    model = WoCategory\n    form_class = WoCategoryForm\n    template_name = 'sales_distribution/wocategory/_wocategory_form.html' # Partial template for modal\n    success_url = reverse_lazy('wocategory_list') # Fallback for non-HTMX requests\n\n    def form_valid(self, form):\n        response = super().form_valid(form)\n        messages.success(self.request, 'Work Order Category updated successfully.')\n        if self.request.headers.get('HX-Request'):\n            return HttpResponse(\n                status=204,\n                headers={\n                    'HX-Trigger': 'refreshWoCategoryList, showToastMessage'\n                }\n            )\n        return response\n\nclass WoCategoryDeleteView(DeleteView):\n    \"\"\"\n    Handles deletion of Work Order Categories.\n    Responds with HTMX triggers for dynamic updates.\n    \"\"\"\n    model = WoCategory\n    template_name = 'sales_distribution/wocategory/confirm_delete.html' # Partial template for modal\n    success_url = reverse_lazy('wocategory_list') # Fallback for non-HTMX requests\n\n    def delete(self, request, *args, **kwargs):\n        \"\"\"\n        Overrides delete to add HTMX specific headers and messages.\n        Includes business logic check from model.\n        \"\"\"\n        self.object = self.get_object()\n        \n        # Call the business logic method from the object\n        if not self.object.can_be_deleted():\n            messages.error(self.request, \"This category cannot be deleted due to associated records.\")\n            if request.headers.get('HX-Request'):\n                # For HTMX, render error message back into the modal or a toast\n                return HttpResponse(\n                    render_to_string('sales_distribution/wocategory/delete_error.html', {'object': self.object, 'messages': messages.get_messages(request)}, request),\n                    status=400 # Indicate a bad request/client error\n                )\n            return HttpResponseRedirect(self.get_success_url()) # Fallback for non-HTMX\n\n        response = super().delete(request, *args, **kwargs)\n        messages.success(self.request, 'Work Order Category deleted successfully.')\n        if request.headers.get('HX-Request'):\n            return HttpResponse(\n                status=204,\n                headers={\n                    'HX-Trigger': 'refreshWoCategoryList, showToastMessage'\n                }\n            )\n        return response"], "forms": ["from django import forms\nfrom .models import WoCategory\n\nclass WoCategoryForm(forms.ModelForm):\n    \"\"\"\n    Form for creating and updating Work Order Categories.\n    \"\"\"\n    class Meta:\n        model = WoCategory\n        fields = ['category_name', 'category_description', 'is_active']\n        widgets = {\n            'category_name': forms.TextInput(attrs={\n                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',\n                'placeholder': 'Enter category name'\n            }),\n            'category_description': forms.Textarea(attrs={\n                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24',\n                'placeholder': 'Enter category description',\n                'rows': 3\n            }),\n            'is_active': forms.CheckboxInput(attrs={\n                'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'\n            }),\n        }\n\n    def clean_category_name(self):\n        \"\"\"\n        Ensure the category name is unique, excluding the current instance during updates.\n        \"\"\"\n        category_name = self.cleaned_data['category_name']\n        query = WoCategory.objects.filter(category_name__iexact=category_name)\n        if self.instance.pk:  # If updating an existing instance\n            query = query.exclude(pk=self.instance.pk)\n        if query.exists():\n            raise forms.ValidationError(\"A category with this name already exists.\")\n        return category_name\n\n    def __init__(self, *args, **kwargs):\n        super().__init__(*args, **kwargs)\n        # Apply additional styling to fields if needed\n        self.fields['category_name'].required = True"], "urls": ["from django.urls import path\nfrom .views import (\n    WoCategoryListView,\n    WoCategoryTablePartialView,\n    WoCategoryCreateView,\n    WoCategoryUpdateView,\n    WoCategoryDeleteView,\n)\n\nurlpatterns = [\n    # Main dashboard view, loads the initial page structure\n    path('wocategory/', WoCategoryListView.as_view(), name='wocategory_list'),\n    \n    # HTMX endpoint for the DataTables table content\n    path('wocategory/table/', WoCategoryTablePartialView.as_view(), name='wocategory_table'),\n\n    # HTMX endpoints for CRUD operations (forms loaded into modal)\n    path('wocategory/add/', WoCategoryCreateView.as_view(), name='wocategory_add'),\n    path('wocategory/edit/<int:pk>/', WoCategoryUpdateView.as_view(), name='wocategory_edit'),\n    path('wocategory/delete/<int:pk>/', WoCategoryDeleteView.as_view(), name='wocategory_delete'),\n]"], "templates": ["{% extends 'core/base.html' %}\n\n{% block title %}Work Order Categories{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <div class=\"flex justify-between items-center mb-6\">\n        <h2 class=\"text-3xl font-extrabold text-gray-900\">Work Order Categories</h2>\n        <button \n            class=\"bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out\"\n            hx-get=\"{% url 'wocategory_add' %}\"\n            hx-target=\"#modalContent\"\n            hx-trigger=\"click\"\n            _=\"on click add .is-active to #modal\">\n            <i class=\"fas fa-plus mr-2\"></i> Add New Category\n        </button>\n    </div>\n    \n    <div id=\"wocategoryTable-container\"\n         hx-trigger=\"load, refreshWoCategoryList from:body\"\n         hx-get=\"{% url 'wocategory_table' %}\"\n         hx-swap=\"innerHTML\"\n         class=\"bg-white shadow-xl rounded-lg p-6\">\n        <!-- Initial loading state -->\n        <div class=\"flex flex-col items-center justify-center py-10\">\n            <div class=\"inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500\"></div>\n            <p class=\"mt-4 text-gray-600 text-lg\">Loading categories...</p>\n        </div>\n    </div>\n    \n    <!-- Modal for form/confirmation -->\n    <div id=\"modal\" class=\"fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 hidden z-50\"\n         _=\"on click if event.target.id == 'modal' remove .is-active from me\"\n         x-data=\"{ show: false }\"\n         x-show=\"show\" x-transition:enter=\"ease-out duration-300\"\n         x-transition:enter-start=\"opacity-0 scale-90\" x-transition:enter-end=\"opacity-100 scale-100\"\n         x-transition:leave=\"ease-in duration-200\" x-transition:leave-start=\"opacity-100 scale-100\"\n         x-transition:leave-end=\"opacity-0 scale-90\">\n        <div id=\"modalContent\" class=\"bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-auto\"\n             _=\"on htmx:afterSwap remove .is-active from #modal end\">\n            <!-- Content loaded via HTMX -->\n        </div>\n    </div>\n</div>\n{% endblock %}\n\n{% block extra_js %}\n<script>\n    // Alpine.js is automatically initialized if included in base.html\n    // DataTables initialization handled within the _wocategory_table.html partial.\n    document.body.addEventListener('htmx:afterSwap', function(evt) {\n        // Close modal after successful form submission (status 204 no content)\n        if (evt.detail.xhr.status === 204) {\n            document.getElementById('modal').classList.remove('is-active');\n            // Hide any open modal if it's managed by Alpine.js\n            let modalElement = document.getElementById('modal');\n            if (modalElement && modalElement.__alpine && modalElement.__alpine.data) {\n                modalElement.__alpine.data.show = false;\n            }\n        }\n    });\n\n    document.body.addEventListener('showToastMessage', function(evt) {\n        // Placeholder for toast message display logic\n        // This would typically involve a separate toast component in base.html\n        // and a custom event listener that reads messages from a global store or a dedicated HTMX response.\n        console.log(\"Show toast message event triggered!\");\n        // Example: You might have an Alpine.js component for toasts\n        // const toastComponent = document.getElementById('toast-container');\n        // if (toastComponent && toastComponent.__alpine) {\n        //     toastComponent.__alpine.data.addToast(message);\n        // }\n    });\n\n</script>\n{% endblock %}", "<table id=\"wocategoryTable\" class=\"min-w-full bg-white table-auto border-collapse\">\n    <thead>\n        <tr class=\"bg-gray-100 border-b border-gray-200\">\n            <th class=\"py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider rounded-tl-lg\">SN</th>\n            <th class=\"py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\">Category Name</th>\n            <th class=\"py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\">Description</th>\n            <th class=\"py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\">Active</th>\n            <th class=\"py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\">Created Date</th>\n            <th class=\"py-3 px-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider rounded-tr-lg\">Actions</th>\n        </tr>\n    </thead>\n    <tbody>\n        {% for obj in wocategories %}\n        <tr class=\"border-b border-gray-200 hover:bg-gray-50\">\n            <td class=\"py-3 px-4 text-sm text-gray-800\">{{ forloop.counter }}</td>\n            <td class=\"py-3 px-4 text-sm text-gray-800\">{{ obj.category_name }}</td>\n            <td class=\"py-3 px-4 text-sm text-gray-800 truncate max-w-xs\">{{ obj.category_description|default_if_none:\"-\" }}</td>\n            <td class=\"py-3 px-4 text-sm text-gray-800\">\n                <span class=\"{% if obj.is_active %}text-green-600{% else %}text-red-600{% endif %}\">\n                    {% if obj.is_active %}<i class=\"fas fa-check-circle\"></i> Yes{% else %}<i class=\"fas fa-times-circle\"></i> No{% endif %}\n                </span>\n            </td>\n            <td class=\"py-3 px-4 text-sm text-gray-800\">{{ obj.created_date|date:\"M d, Y H:i\" }}</td>\n            <td class=\"py-3 px-4 text-center\">\n                <button \n                    class=\"bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-1 px-3 rounded-lg shadow-sm mr-2 transition duration-300 ease-in-out\"\n                    hx-get=\"{% url 'wocategory_edit' obj.pk %}\"\n                    hx-target=\"#modalContent\"\n                    hx-trigger=\"click\"\n                    _=\"on click add .is-active to #modal\">\n                    <i class=\"fas fa-edit\"></i> Edit\n                </button>\n                <button \n                    class=\"bg-red-500 hover:bg-red-600 text-white font-medium py-1 px-3 rounded-lg shadow-sm transition duration-300 ease-in-out\"\n                    hx-get=\"{% url 'wocategory_delete' obj.pk %}\"\n                    hx-target=\"#modalContent\"\n                    hx-trigger=\"click\"\n                    _=\"on click add .is-active to #modal\">\n                    <i class=\"fas fa-trash-alt\"></i> Delete\n                </button>\n            </td>\n        </tr>\n        {% empty %}\n        <tr>\n            <td colspan=\"6\" class=\"py-8 text-center text-gray-500 text-lg\">No work order categories found.</td>\n        </tr>\n        {% endfor %}\n    </tbody>\n</table>\n\n<script>\n    // Ensure DataTables is loaded and initialized only once per table load\n    // Check if DataTables has already been initialized on this table\n    if ($.fn.DataTable.isDataTable('#wocategoryTable')) {\n        $('#wocategoryTable').DataTable().destroy();\n    }\n    \n    $('#wocategoryTable').DataTable({\n        \"pageLength\": 10,\n        \"lengthMenu\": [[5, 10, 25, 50, -1], [5, 10, 25, 50, \"All\"]],\n        \"pagingType\": \"full_numbers\",\n        \"responsive\": true,\n        \"language\": {\n            \"search\": \"Filter records:\",\n            \"lengthMenu\": \"Show _MENU_ entries\"\n        }\n    });\n</script>", "<div class=\"bg-white p-6 rounded-lg shadow-lg\">\n    <div class=\"flex justify-between items-center mb-5\">\n        <h3 class=\"text-2xl font-semibold text-gray-900\">{{ form.instance.pk|yesno:'Edit,Add' }} Work Order Category</h3>\n        <button type=\"button\" class=\"text-gray-400 hover:text-gray-600 transition-colors duration-200\"\n                _=\"on click remove .is-active from #modal\">\n            <i class=\"fas fa-times text-xl\"></i>\n        </button>\n    </div>\n    \n    <form hx-post=\"{{ request.path }}\" hx-swap=\"none\" hx-indicator=\"#form-spinner\">\n        {% csrf_token %}\n        \n        <div class=\"space-y-5\">\n            {% for field in form %}\n            <div class=\"relative\">\n                <label for=\"{{ field.id_for_label }}\" class=\"block text-sm font-medium text-gray-700 mb-1\">\n                    {{ field.label }}\n                    {% if field.field.required %}<span class=\"text-red-500\">*</span>{% endif %}\n                </label>\n                {{ field }}\n                {% if field.help_text %}\n                <p class=\"mt-1 text-xs text-gray-500\">{{ field.help_text }}</p>\n                {% endif %}\n                {% if field.errors %}\n                <ul class=\"text-red-600 text-sm mt-1 list-disc pl-5\">\n                    {% for error in field.errors %}\n                    <li>{{ error }}</li>\n                    {% endfor %}\n                </ul>\n                {% endif %}\n            </div>\n            {% endfor %}\n        </div>\n        \n        <div class=\"mt-8 flex items-center justify-end space-x-4\">\n            <button \n                type=\"button\" \n                class=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out\"\n                _=\"on click remove .is-active from #modal\">\n                Cancel\n            </button>\n            <button \n                type=\"submit\" \n                class=\"bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out\">\n                <i class=\"fas fa-save mr-2\"></i> Save Changes\n            </button>\n            <div id=\"form-spinner\" class=\"htmx-indicator ml-3\">\n                <div class=\"inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500\"></div>\n            </div>\n        </div>\n    </form>\n</div>", "<div class=\"bg-white p-6 rounded-lg shadow-lg\">\n    <div class=\"flex justify-between items-center mb-5\">\n        <h3 class=\"text-2xl font-semibold text-gray-900\">Confirm Deletion</h3>\n        <button type=\"button\" class=\"text-gray-400 hover:text-gray-600 transition-colors duration-200\"\n                _=\"on click remove .is-active from #modal\">\n            <i class=\"fas fa-times text-xl\"></i>\n        </button>\n    </div>\n    <p class=\"text-gray-700 mb-6\">Are you sure you want to delete the Work Order Category: <strong class=\"text-red-600\">\"{{ object.category_name }}\"</strong>?</p>\n    \n    <div class=\"mt-6 flex justify-end space-x-4\">\n        <button \n            type=\"button\" \n            class=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out\"\n            _=\"on click remove .is-active from #modal\">\n            Cancel\n        </button>\n        <button \n            hx-post=\"{% url 'wocategory_delete' object.pk %}\" \n            hx-swap=\"none\"\n            hx-confirm=\"This action cannot be undone. Are you absolutely sure?\"\n            class=\"bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out\">\n            <i class=\"fas fa-trash-alt mr-2\"></i> Delete\n        </button>\n    </div>\n</div>", "<div class=\"bg-white p-6 rounded-lg shadow-lg\">\n    <div class=\"flex justify-between items-center mb-5\">\n        <h3 class=\"text-2xl font-semibold text-red-700\">Deletion Failed</h3>\n        <button type=\"button\" class=\"text-gray-400 hover:text-gray-600 transition-colors duration-200\"\n                _=\"on click remove .is-active from #modal\">\n            <i class=\"fas fa-times text-xl\"></i>\n        </button>\n    </div>\n    <p class=\"text-gray-700 mb-6\">The Work Order Category \"<strong class=\"text-red-600\">{{ object.category_name }}</strong>\" cannot be deleted.</p>\n    <p class=\"text-gray-700 mb-6\">{% for message in messages %}{{ message }}{% endfor %}</p>\n    \n    <div class=\"mt-6 flex justify-end\">\n        <button \n            type=\"button\" \n            class=\"bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out\"\n            _=\"on click remove .is-active from #modal\">\n            OK\n        </button>\n    </div>\n</div>"], "tests": ["from django.test import TestCase, Client\nfrom django.urls import reverse\nfrom .models import WoCategory\nfrom .forms import WoCategoryForm\nfrom datetime import datetime, timedelta\nimport json\n\nclass WoCategoryModelTest(TestCase):\n    \"\"\"\n    Unit tests for the WoCategory model.\n    \"\"\"\n    @classmethod\n    def setUpTestData(cls):\n        # Create a single test category for all tests\n        cls.category1 = WoCategory.objects.create(\n            category_id=1,  # Assuming DB manages this, but for tests, we set it.\n            category_name='Test Category One',\n            category_description='Description for test category one.',\n            is_active=True,\n            created_date=datetime.now() - timedelta(days=5)\n        )\n        cls.category2 = WoCategory.objects.create(\n            category_id=2,\n            category_name='Another Category',\n            category_description='Description for another category.',\n            is_active=False,\n            created_date=datetime.now() - timedelta(days=1)\n        )\n  \n    def test_wocategory_creation(self):\n        \"\"\"Test that a WoCategory object can be created successfully.\"\"\"\n        self.assertEqual(self.category1.category_name, 'Test Category One')\n        self.assertEqual(self.category1.is_active, True)\n        self.assertTrue(WoCategory.objects.filter(category_id=1).exists())\n        self.assertEqual(WoCategory.objects.count(), 2)\n        \n    def test_category_name_label(self):\n        \"\"\"Test the verbose name for category_name field.\"\"\"\n        field_label = self.category1._meta.get_field('category_name').verbose_name\n        self.assertEqual(field_label, 'Category Name')\n        \n    def test_str_representation(self):\n        \"\"\"Test the __str__ method returns the category name.\"\"\"\n        self.assertEqual(str(self.category1), 'Test Category One')\n\n    def test_can_be_deleted_method(self):\n        \"\"\"Test the custom can_be_deleted business logic method.\"\"\"\n        # As implemented, it always returns True for now.\n        self.assertTrue(self.category1.can_be_deleted())\n        # In a real scenario, you'd mock related objects or create more complex test data.\n\n    def test_unique_category_name_validation(self):\n        \"\"\"Test that category names must be unique.\"\"\"\n        form_data = {\n            'category_name': 'Test Category One', # This name already exists\n            'category_description': 'Duplicate description',\n            'is_active': True\n        }\n        form = WoCategoryForm(data=form_data)\n        self.assertFalse(form.is_valid())\n        self.assertIn('category_name', form.errors)\n        self.assertIn('A category with this name already exists.', form.errors['category_name'])\n    \n    def test_unique_category_name_validation_on_update(self):\n        \"\"\"Test unique validation doesn't block saving the same instance.\"\"\"\n        form_data = {\n            'category_name': 'Test Category One',\n            'category_description': 'Updated description',\n            'is_active': True\n        }\n        form = WoCategoryForm(instance=self.category1, data=form_data)\n        self.assertTrue(form.is_valid())\n        self.assertEqual(form.cleaned_data['category_name'], 'Test Category One')\n\n\nclass WoCategoryViewsTest(TestCase):\n    \"\"\"\n    Integration tests for WoCategory views.\n    \"\"\"\n    @classmethod\n    def setUpTestData(cls):\n        # Create test data for all tests\n        cls.category1 = WoCategory.objects.create(\n            category_id=101,\n            category_name='Existing Category',\n            category_description='Description for existing category.',\n            is_active=True,\n            created_date=datetime.now()\n        )\n        cls.category2 = WoCategory.objects.create(\n            category_id=102,\n            category_name='Another Existing',\n            category_description='Another description.',\n            is_active=False,\n            created_date=datetime.now()\n        )\n    \n    def setUp(self):\n        self.client = Client()\n    \n    def test_list_view(self):\n        \"\"\"Test the main category list page loads correctly.\"\"\"\n        response = self.client.get(reverse('wocategory_list'))\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'sales_distribution/wocategory/list.html')\n        # The list.html template now uses HTMX to load content, so we don't check context here directly.\n        \n    def test_table_partial_view(self):\n        \"\"\"Test the HTMX partial for the category table.\"\"\"\n        response = self.client.get(reverse('wocategory_table'))\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'sales_distribution/wocategory/_wocategory_table.html')\n        self.assertIn('wocategories', response.context)\n        self.assertEqual(len(response.context['wocategories']), 2)\n        self.assertContains(response, 'Existing Category')\n        self.assertContains(response, 'Another Existing')\n\n    def test_create_view_get(self):\n        \"\"\"Test GET request for the add category form.\"\"\"\n        response = self.client.get(reverse('wocategory_add'), HTTP_HX_REQUEST='true') # Simulate HTMX request\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'sales_distribution/wocategory/_wocategory_form.html')\n        self.assertIn('form', response.context)\n        self.assertIsInstance(response.context['form'], WoCategoryForm)\n        \n    def test_create_view_post_htmx_success(self):\n        \"\"\"Test POST request for adding a new category with HTMX.\"\"\"\n        data = {\n            'category_name': 'New Category From HTMX',\n            'category_description': 'Description for new category.',\n            'is_active': True,\n        }\n        # Simulate HTMX request for the POST\n        response = self.client.post(reverse('wocategory_add'), data, HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 204) # HTMX success code\n        self.assertIn('HX-Trigger', response.headers)\n        self.assertIn('refreshWoCategoryList', response.headers['HX-Trigger'])\n        self.assertTrue(WoCategory.objects.filter(category_name='New Category From HTMX').exists())\n        self.assertEqual(WoCategory.objects.count(), 3) # Two existing + one new\n        \n    def test_create_view_post_non_htmx_success(self):\n        \"\"\"Test POST request for adding a new category without HTMX (redirect).\"\"\"\n        data = {\n            'category_name': 'New Category Non HTMX',\n            'category_description': 'Description for non-htmx category.',\n            'is_active': False,\n        }\n        response = self.client.post(reverse('wocategory_add'), data)\n        self.assertEqual(response.status_code, 302) # Redirect for non-HTMX\n        self.assertRedirects(response, reverse('wocategory_list'))\n        self.assertTrue(WoCategory.objects.filter(category_name='New Category Non HTMX').exists())\n        self.assertEqual(WoCategory.objects.count(), 3)\n        \n    def test_create_view_post_invalid(self):\n        \"\"\"Test POST request with invalid data.\"\"\"\n        data = {\n            'category_name': 'Existing Category', # Duplicate name\n            'category_description': 'Description',\n            'is_active': True,\n        }\n        response = self.client.post(reverse('wocategory_add'), data, HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 200) # Form re-rendered with errors\n        self.assertTemplateUsed(response, 'sales_distribution/wocategory/_wocategory_form.html')\n        self.assertContains(response, 'A category with this name already exists.')\n        self.assertEqual(WoCategory.objects.count(), 2) # No new object created\n\n    def test_update_view_get(self):\n        \"\"\"Test GET request for the edit category form.\"\"\"\n        response = self.client.get(reverse('wocategory_edit', args=[self.category1.pk]), HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'sales_distribution/wocategory/_wocategory_form.html')\n        self.assertIn('form', response.context)\n        self.assertEqual(response.context['form'].instance, self.category1)\n        self.assertContains(response, self.category1.category_name)\n        \n    def test_update_view_post_htmx_success(self):\n        \"\"\"Test POST request for updating a category with HTMX.\"\"\"\n        data = {\n            'category_name': 'Updated Category Name',\n            'category_description': 'Updated description.',\n            'is_active': False,\n        }\n        response = self.client.post(reverse('wocategory_edit', args=[self.category1.pk]), data, HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 204)\n        self.assertIn('HX-Trigger', response.headers)\n        self.assertIn('refreshWoCategoryList', response.headers['HX-Trigger'])\n        self.category1.refresh_from_db()\n        self.assertEqual(self.category1.category_name, 'Updated Category Name')\n        self.assertEqual(self.category1.is_active, False)\n        \n    def test_update_view_post_non_htmx_success(self):\n        \"\"\"Test POST request for updating a category without HTMX (redirect).\"\"\"\n        data = {\n            'category_name': 'Updated Category No HTMX',\n            'category_description': 'Updated description.',\n            'is_active': True,\n        }\n        response = self.client.post(reverse('wocategory_edit', args=[self.category2.pk]), data)\n        self.assertEqual(response.status_code, 302)\n        self.assertRedirects(response, reverse('wocategory_list'))\n        self.category2.refresh_from_db()\n        self.assertEqual(self.category2.category_name, 'Updated Category No HTMX')\n\n    def test_delete_view_get(self):\n        \"\"\"Test GET request for delete confirmation.\"\"\"\n        response = self.client.get(reverse('wocategory_delete', args=[self.category1.pk]), HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'sales_distribution/wocategory/confirm_delete.html')\n        self.assertIn('object', response.context)\n        self.assertEqual(response.context['object'], self.category1)\n        self.assertContains(response, f'delete the Work Order Category: \"{self.category1.category_name}\"')\n        \n    def test_delete_view_post_htmx_success(self):\n        \"\"\"Test POST request for deleting a category with HTMX.\"\"\"\n        category_to_delete = WoCategory.objects.create(category_id=103, category_name='Temp Delete', is_active=True)\n        self.assertEqual(WoCategory.objects.count(), 3) # Initial count\n        \n        response = self.client.post(reverse('wocategory_delete', args=[category_to_delete.pk]), HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 204)\n        self.assertIn('HX-Trigger', response.headers)\n        self.assertIn('refreshWoCategoryList', response.headers['HX-Trigger'])\n        self.assertFalse(WoCategory.objects.filter(pk=category_to_delete.pk).exists())\n        self.assertEqual(WoCategory.objects.count(), 2) # Count after deletion\n\n    def test_delete_view_post_non_htmx_success(self):\n        \"\"\"Test POST request for deleting a category without HTMX (redirect).\"\"\"\n        category_to_delete = WoCategory.objects.create(category_id=104, category_name='Temp Delete 2', is_active=True)\n        self.assertEqual(WoCategory.objects.count(), 3)\n        \n        response = self.client.post(reverse('wocategory_delete', args=[category_to_delete.pk]))\n        self.assertEqual(response.status_code, 302)\n        self.assertRedirects(response, reverse('wocategory_list'))\n        self.assertFalse(WoCategory.objects.filter(pk=category_to_delete.pk).exists())\n        self.assertEqual(WoCategory.objects.count(), 2)\n    \n    # Test for the can_be_deleted business logic (if implemented to return False sometimes)\n    # def test_delete_view_post_blocked_by_business_logic(self):\n    #     category_undel = WoCategory.objects.create(category_id=105, category_name='Cannot Delete', is_active=True)\n    #     # Temporarily patch the can_be_deleted method for this test\n    #     with self.patch.object(WoCategory, 'can_be_deleted', return_value=False):\n    #         response = self.client.post(reverse('wocategory_delete', args=[category_undel.pk]), HTTP_HX_REQUEST='true')\n    #         self.assertEqual(response.status_code, 400) # Or 200, depending on how you handle the error\n    #         self.assertTemplateUsed(response, 'sales_distribution/wocategory/delete_error.html')\n    #         self.assertContains(response, \"This category cannot be deleted due to associated records.\")\n    #         self.assertTrue(WoCategory.objects.filter(pk=category_undel.pk).exists()) # Should still exist"], "admin": []}