{"models": ["# salesdistribution/models.py\nfrom django.db import models\n\nclass Department(models.Model):\n    \"\"\"\n    Represents a department from tblHR_Departments.\n    \"\"\"\n    id = models.IntegerField(db_column='Id', primary_key=True)\n    symbol = models.CharField(db_column='Symbol', max_length=255) # Assuming max_length for Symbol\n\n    class Meta:\n        managed = False  # Tells Django not to manage this table's schema in migrations\n        db_table = 'tblHR_Departments'\n        verbose_name = 'Department'\n        verbose_name_plural = 'Departments'\n\n    def __str__(self):\n        return self.symbol\n\nclass OfficeStaff(models.Model):\n    \"\"\"\n    Represents an office staff member from tblHR_OfficeStaff,\n    including their work order release (WR) and dispatch authority (DA) permissions.\n    \"\"\"\n    # Assuming EmpId is a unique identifier, not Django's default PK.\n    # If EmpId is the primary key in your actual database, set primary_key=True.\n    emp_id = models.CharField(db_column='EmpId', max_length=50, unique=True)\n    title = models.CharField(db_column='Title', max_length=50, null=True, blank=True)\n    employee_name = models.CharField(db_column='EmployeeName', max_length=255)\n    department = models.ForeignKey(Department, models.DO_NOTHING, db_column='Department', null=True, blank=True)\n    wr = models.BooleanField(db_column='WR', default=False)  # Work Order Release permission\n    da = models.BooleanField(db_column='DA', default=False)  # Dispatch Authority permission\n    comp_id = models.IntegerField(db_column='CompId')  # Company Identifier\n    resignation_date = models.DateField(db_column='ResignationDate', null=True, blank=True) # Date of resignation\n    user_id = models.IntegerField(db_column='UserID', null=True, blank=True) # Associated User ID\n\n    class Meta:\n        managed = False\n        db_table = 'tblHR_OfficeStaff'\n        verbose_name = 'Office Staff Member'\n        verbose_name_plural = 'Office Staff Members'\n\n    def __str__(self):\n        return f\"{self.full_employee_name} ({self.emp_id})\"\n\n    @property\n    def full_employee_name(self):\n        \"\"\"Combines title and employee name for display.\"\"\"\n        if self.title:\n            return f\"{self.title}. {self.employee_name}\"\n        return self.employee_name\n\n    @classmethod\n    def update_employee_permissions(cls, emp_id, wr_status, da_status, company_id):\n        \"\"\"\n        Business logic to update WR and DA permissions for a specific employee\n        within a given company. This adheres to the 'fat model' principle.\n        \"\"\"\n        try:\n            # Retrieve the specific staff member using emp_id and company_id\n            staff_member = cls.objects.get(emp_id=emp_id, comp_id=company_id)\n            # Update the permission fields\n            staff_member.wr = wr_status\n            staff_member.da = da_status\n            # Save the changes to the database\n            staff_member.save()\n            return True  # Indicate successful update\n        except cls.DoesNotExist:\n            return False # Indicate that the staff member was not found\n        except Exception as e:\n            # Log the error (in a real application)\n            print(f\"Error updating permissions for EmpId {emp_id}: {e}\")\n            return False # Indicate failure due to an exception"], "views": ["# salesdistribution/views.py\nfrom django.views.generic import TemplateView\nfrom django.urls import reverse_lazy\nfrom django.contrib import messages\nfrom django.http import HttpResponseRedirect, HttpResponse\nfrom django.shortcuts import render\nfrom django.db import transaction\n\nfrom .models import OfficeStaff, Department\nfrom .forms import OfficeStaffPermissionFormSet\n\nclass OfficeStaffPermissionView(TemplateView):\n    \"\"\"\n    Manages the display and bulk update of Work Order Release (WR) and Dispatch Authority (DA)\n    permissions for office staff members.\n    \"\"\"\n    template_name = 'salesdistribution/wo_release_da/list.html'\n    \n    def get_context_data(self, **kwargs):\n        \"\"\"\n        Prepares the context for displaying the employee permissions grid.\n        Fetches active employees and their current permission states to populate the formset.\n        \"\"\"\n        context = super().get_context_data(**kwargs)\n        \n        # In a real application, current_comp_id would come from request.user.profile.comp_id\n        # For this example, we'll use a placeholder company ID.\n        current_comp_id = 1 \n\n        # Retrieve active employees matching ASP.NET filtering logic\n        staff_members = OfficeStaff.objects.filter(\n            comp_id=current_comp_id,\n            resignation_date__isnull=True, # Employees who have not resigned\n        ).exclude(\n            employee_name__iexact='ERP' # Exclude 'ERP' user\n        ).exclude(\n            user_id=1 # Exclude user with ID '1'\n        ).select_related('department').order_by('employee_name') # Optimize department lookup and order\n\n        initial_data = []\n        for member in staff_members:\n            # Prepare initial data for each form in the formset\n            initial_data.append({\n                'emp_id': member.emp_id,\n                'wr': member.wr,\n                'da': member.da,\n            })\n        \n        # Populate the formset with initial data based on current permissions\n        context['formset'] = OfficeStaffPermissionFormSet(initial=initial_data)\n        \n        # Also pass the full staff member objects for displaying name, emp no, department symbol\n        # as these are not part of the form fields themselves\n        context['staff_members_display_data'] = staff_members\n\n        return context\n\n    def post(self, request, *args, **kwargs):\n        \"\"\"\n        Handles the submission of the employee permissions formset.\n        Validates the data and updates permissions via the model's business logic.\n        \"\"\"\n        # In a real application, current_comp_id would come from request.user.profile.comp_id\n        current_comp_id = 1 \n\n        # Instantiate the formset with POST data\n        formset = OfficeStaffPermissionFormSet(request.POST)\n\n        if formset.is_valid():\n            # Use a database transaction to ensure all updates succeed or none do\n            with transaction.atomic():\n                for form in formset:\n                    emp_id = form.cleaned_data['emp_id']\n                    wr_status = form.cleaned_data['wr']\n                    da_status = form.cleaned_data['da']\n                    \n                    # Call the 'fat model' method to update permissions\n                    OfficeStaff.update_employee_permissions(emp_id, wr_status, da_status, current_comp_id)\n            \n            messages.success(self.request, 'Employee permissions updated successfully.')\n            \n            # HTMX response for success: 204 No Content with a trigger to refresh the list\n            if request.headers.get('HX-Request'):\n                return HttpResponse(\n                    status=204,\n                    headers={'HX-Trigger': 'refreshPermissionsList'}\n                )\n            # Fallback for non-HTMX requests (should not happen with this setup)\n            return HttpResponseRedirect(self.request.path_info)\n        else:\n            # If formset is invalid, re-render the page with errors\n            messages.error(self.request, 'There were errors updating permissions. Please check the form.')\n            # Re-call get_context_data to get staff_members_display_data, and then override formset with invalid one\n            context = self.get_context_data() \n            context['formset'] = formset # Pass the formset with errors back to the template\n            return render(request, self.template_name, context)"], "forms": ["# salesdistribution/forms.py\nfrom django import forms\nfrom .models import OfficeStaff\n\nclass OfficeStaffPermissionForm(forms.Form):\n    \"\"\"\n    A form representing a single row in the employee permissions grid.\n    It captures the employee ID (hidden) and their Release (WR) and Dispatch (DA) permissions.\n    \"\"\"\n    emp_id = forms.CharField(widget=forms.HiddenInput())\n    wr = forms.BooleanField(\n        required=False,\n        widget=forms.CheckboxInput(attrs={'class': 'form-checkbox h-5 w-5 text-blue-600 rounded'})\n    )\n    da = forms.BooleanField(\n        required=False,\n        widget=forms.CheckboxInput(attrs={'class': 'form-checkbox h-5 w-5 text-blue-600 rounded'})\n    )\n\n# Create a formset factory. 'extra=0' means no empty forms are added by default.\nOfficeStaffPermissionFormSet = forms.formset_factory(OfficeStaffPermissionForm, extra=0)"], "urls": ["# salesdistribution/urls.py\nfrom django.urls import path\nfrom .views import OfficeStaffPermissionView\n\napp_name = 'salesdistribution' # Namespace for URLs\n\nurlpatterns = [\n    # Main URL for the permission management page\n    path('wo_release_da/', OfficeStaffPermissionView.as_view(), name='wo_release_da_list'),\n    # HTMX-specific URL to load only the table content (used for initial load and refresh)\n    path('wo_release_da/table/', OfficeStaffPermissionView.as_view(), name='wo_release_da_table'),\n]"], "templates": ["{# salesdistribution/wo_release_da/list.html #}\n{% extends 'core/base.html' %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <div class=\"flex justify-between items-center mb-6\">\n        <h2 class=\"text-2xl font-bold text-gray-800\">Work Order Release & Dispatch Authority</h2>\n    </div>\n    \n    <div id=\"permissions-table-container\"\n         hx-trigger=\"load, refreshPermissionsList from:body\"\n         hx-get=\"{% url 'salesdistribution:wo_release_da_table' %}\"\n         hx-swap=\"innerHTML\">\n        <!-- Loading spinner while content loads -->\n        <div class=\"text-center py-10\">\n            <div class=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n            <p class=\"mt-2 text-gray-600\">Loading employee permissions...</p>\n        </div>\n    </div>\n    \n    {# No modal needed for this bulk update page based on ASP.NET functionality #}\n</div>\n{% endblock %}\n\n{% block extra_js %}\n<script>\n    document.addEventListener('alpine:init', () => {\n        // Alpine.js component initialization if needed for general UI state\n        // This specific page doesn't require complex Alpine.js for its core functionality\n        // as HTMX handles the interactions.\n    });\n</script>\n{% endblock %}", "{# salesdistribution/wo_release_da/_permission_table.html #}\n{# This partial is loaded via HTMX into #permissions-table-container #}\n\n<form hx-post=\"{% url 'salesdistribution:wo_release_da_list' %}\"\n      hx-swap=\"outerHTML\"\n      hx-target=\"#permissions-table-container\"\n      class=\"bg-white shadow-sm rounded-lg p-6\">\n    {% csrf_token %}\n    {{ formset.management_form }} {# Required for formsets #}\n\n    <div class=\"overflow-x-auto\">\n        <table id=\"employeePermissionsTable\" class=\"min-w-full divide-y divide-gray-200\">\n            <thead class=\"bg-gray-50\">\n                <tr>\n                    <th class=\"py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">SN</th>\n                    <th class=\"py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Name of Employee</th>\n                    <th class=\"py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Emp. No</th>\n                    <th class=\"py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Department</th>\n                    <th class=\"py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\">Release</th>\n                    <th class=\"py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\">Dispatch</th>\n                </tr>\n            </thead>\n            <tbody class=\"bg-white divide-y divide-gray-200\">\n                {% for form in formset %}\n                <tr>\n                    <td class=\"py-2 px-4 border-b border-gray-200 text-right\">{{ forloop.counter }}</td>\n                    <td class=\"py-2 px-4 border-b border-gray-200\">\n                        {# Display data from staff_members_display_data as it's not part of the form #}\n                        {{ staff_members_display_data.forloop.counter0.full_employee_name }}\n                    </td>\n                    <td class=\"py-2 px-4 border-b border-gray-200 text-center\">\n                        {{ staff_members_display_data.forloop.counter0.emp_id }}\n                        {{ form.emp_id }} {# Hidden input for emp_id #}\n                    </td>\n                    <td class=\"py-2 px-4 border-b border-gray-200 text-center\">\n                        {{ staff_members_display_data.forloop.counter0.department.symbol|default:\"N/A\" }}\n                    </td>\n                    <td class=\"py-2 px-4 border-b border-gray-200 text-center\">\n                        {{ form.wr }}\n                        {% if form.wr.errors %}\n                        <p class=\"text-red-500 text-xs mt-1\">{{ form.wr.errors }}</p>\n                        {% endif %}\n                    </td>\n                    <td class=\"py-2 px-4 border-b border-gray-200 text-center\">\n                        {{ form.da }}\n                        {% if form.da.errors %}\n                        <p class=\"text-red-500 text-xs mt-1\">{{ form.da.errors }}</p>\n                        {% endif %}\n                    </td>\n                </tr>\n                {% endfor %}\n            </tbody>\n        </table>\n    </div>\n\n    <div class=\"mt-6 text-center\">\n        <button type=\"submit\" class=\"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out\">\n            Submit Permissions\n        </button>\n    </div>\n</form>\n\n{# Initialize DataTables #}\n<script>\n    $(document).ready(function() {\n        $('#employeePermissionsTable').DataTable({\n            \"pageLength\": 10,\n            \"lengthMenu\": [[10, 25, 50, -1], [10, 25, 50, \"All\"]],\n            \"columnDefs\": [\n                { \"orderable\": false, \"targets\": [4, 5] } // Disable sorting for 'Release' and 'Dispatch' checkbox columns\n            ]\n        });\n    });\n</script>"], "tests": ["# salesdistribution/tests.py\nfrom django.test import TestCase, Client\nfrom django.urls import reverse\nfrom django.db import connection\n\nfrom salesdistribution.models import OfficeStaff, Department\n\nclass OfficeStaffModelTest(TestCase):\n    \"\"\"\n    Tests for the OfficeStaff model and its business logic.\n    \"\"\"\n    @classmethod\n    def setUpTestData(cls):\n        # Setup test database structure using raw SQL for unmanaged models\n        with connection.cursor() as cursor:\n            cursor.execute(\"\"\"\n                CREATE TABLE tblHR_Departments (\n                    Id INT PRIMARY KEY,\n                    Symbol NVARCHAR(50)\n                );\n            \"\")\n            cursor.execute(\"\"\"\n                CREATE TABLE tblHR_OfficeStaff (\n                    EmpId NVARCHAR(50) PRIMARY KEY,\n                    Title NVARCHAR(50),\n                    EmployeeName NVARCHAR(255),\n                    Department INT,\n                    WR BIT,\n                    DA BIT,\n                    CompId INT,\n                    ResignationDate DATE,\n                    UserID INT\n                );\n            \"\")\n\n            # Insert sample data for testing\n            Department.objects.create(id=1, symbol='IT')\n            Department.objects.create(id=2, symbol='HR')\n\n            OfficeStaff.objects.create(\n                emp_id='EMP001',\n                title='Mr',\n                employee_name='<PERSON>',\n                department_id=1,\n                wr=False,\n                da=False,\n                comp_id=1,\n                resignation_date=None,\n                user_id=101\n            )\n            OfficeStaff.objects.create(\n                emp_id='EMP002',\n                title='Ms',\n                employee_name='Jane Smith',\n                department_id=2,\n                wr=True,\n                da=False,\n                comp_id=1,\n                resignation_date=None,\n                user_id=102\n            )\n            OfficeStaff.objects.create(\n                emp_id='EMP003',\n                title='Dr',\n                employee_name='ERP User', # Should be excluded\n                department_id=1,\n                wr=False,\n                da=False,\n                comp_id=1,\n                resignation_date=None,\n                user_id=103\n            )\n            OfficeStaff.objects.create(\n                emp_id='EMP004',\n                title='Mr',\n                employee_name='Inactive User', # Should be excluded\n                department_id=1,\n                wr=False,\n                da=False,\n                comp_id=1,\n                resignation_date='2023-01-01',\n                user_id=104\n            )\n            OfficeStaff.objects.create(\n                emp_id='EMP005',\n                title='Mr',\n                employee_name='System User', # Should be excluded by UserID\n                department_id=1,\n                wr=False,\n                da=False,\n                comp_id=1,\n                resignation_date=None,\n                user_id=1\n            )\n            OfficeStaff.objects.create(\n                emp_id='EMP006',\n                title='Mrs',\n                employee_name='Sarah Johnson',\n                department_id=2,\n                wr=True,\n                da=True,\n                comp_id=2, # Different company ID\n                resignation_date=None,\n                user_id=105\n            )\n    \n    def test_office_staff_creation(self):\n        \"\"\"Test that OfficeStaff objects are created correctly.\"\"\"\n        emp1 = OfficeStaff.objects.get(emp_id='EMP001')\n        self.assertEqual(emp1.employee_name, 'John Doe')\n        self.assertEqual(emp1.department.symbol, 'IT')\n        self.assertFalse(emp1.wr)\n        self.assertFalse(emp1.da)\n\n    def test_full_employee_name_property(self):\n        \"\"\"Test the full_employee_name property.\"\"\"\n        emp1 = OfficeStaff.objects.get(emp_id='EMP001')\n        self.assertEqual(emp1.full_employee_name, 'Mr. John Doe')\n        \n        # Test case with no title\n        emp_no_title = OfficeStaff.objects.create(\n            emp_id='EMP_NT', employee_name='No Title', department_id=1, comp_id=1\n        )\n        self.assertEqual(emp_no_title.full_employee_name, 'No Title')\n\n    def test_update_employee_permissions_success(self):\n        \"\"\"Test successful update of employee permissions.\"\"\"\n        initial_emp = OfficeStaff.objects.get(emp_id='EMP001')\n        self.assertFalse(initial_emp.wr)\n        self.assertFalse(initial_emp.da)\n\n        success = OfficeStaff.update_employee_permissions('EMP001', True, True, 1)\n        self.assertTrue(success)\n\n        updated_emp = OfficeStaff.objects.get(emp_id='EMP001')\n        self.assertTrue(updated_emp.wr)\n        self.assertTrue(updated_emp.da)\n\n    def test_update_employee_permissions_not_found(self):\n        \"\"\"Test update fails when employee is not found.\"\"\"\n        success = OfficeStaff.update_employee_permissions('NONEXISTENT', True, True, 1)\n        self.assertFalse(success)\n\n    def test_update_employee_permissions_wrong_company(self):\n        \"\"\"Test update fails when company ID does not match.\"\"\"\n        emp_diff_comp = OfficeStaff.objects.get(emp_id='EMP006')\n        self.assertTrue(emp_diff_comp.wr) # Initially True\n\n        success = OfficeStaff.update_employee_permissions('EMP006', False, False, 999) # Wrong company ID\n        self.assertFalse(success)\n\n        # Verify permissions did not change\n        emp_diff_comp_after = OfficeStaff.objects.get(emp_id='EMP006')\n        self.assertTrue(emp_diff_comp_after.wr)\n\n\nclass OfficeStaffPermissionViewTest(TestCase):\n    \"\"\"\n    Integration tests for the OfficeStaffPermissionView.\n    \"\"\"\n    @classmethod\n    def setUpTestData(cls):\n        # Setup test database structure (same as ModelTest for consistency)\n        with connection.cursor() as cursor:\n            cursor.execute(\"\"\"\n                CREATE TABLE tblHR_Departments (\n                    Id INT PRIMARY KEY,\n                    Symbol NVARCHAR(50)\n                );\n            \"\")\n            cursor.execute(\"\"\"\n                CREATE TABLE tblHR_OfficeStaff (\n                    EmpId NVARCHAR(50) PRIMARY KEY,\n                    Title NVARCHAR(50),\n                    EmployeeName NVARCHAR(255),\n                    Department INT,\n                    WR BIT,\n                    DA BIT,\n                    CompId INT,\n                    ResignationDate DATE,\n                    UserID INT\n                );\n            \"\")\n\n            Department.objects.create(id=1, symbol='IT')\n            Department.objects.create(id=2, symbol='HR')\n\n            OfficeStaff.objects.create(\n                emp_id='EMP001', title='Mr', employee_name='John Doe', department_id=1,\n                wr=False, da=False, comp_id=1, resignation_date=None, user_id=101\n            )\n            OfficeStaff.objects.create(\n                emp_id='EMP002', title='Ms', employee_name='Jane Smith', department_id=2,\n                wr=True, da=False, comp_id=1, resignation_date=None, user_id=102\n            )\n            OfficeStaff.objects.create(\n                emp_id='EMP003', title='Dr', employee_name='ERP User', department_id=1,\n                wr=False, da=False, comp_id=1, resignation_date=None, user_id=103\n            )\n            OfficeStaff.objects.create(\n                emp_id='EMP004', title='Mr', employee_name='Inactive User', department_id=1,\n                wr=False, da=False, comp_id=1, resignation_date='2023-01-01', user_id=104\n            )\n            OfficeStaff.objects.create(\n                emp_id='EMP005', title='Mr', employee_name='System User', department_id=1,\n                wr=False, da=False, comp_id=1, resignation_date=None, user_id=1\n            )\n            OfficeStaff.objects.create(\n                emp_id='EMP006', title='Mrs', employee_name='Sarah Johnson', department_id=2,\n                wr=True, da=True, comp_id=2, resignation_date=None, user_id=105\n            )\n\n    def setUp(self):\n        self.client = Client()\n        self.list_url = reverse('salesdistribution:wo_release_da_list')\n        self.table_url = reverse('salesdistribution:wo_release_da_table') # For HTMX partial\n\n    def test_list_view_get(self):\n        \"\"\"Test GET request to the main list view.\"\"\"\n        response = self.client.get(self.list_url)\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'salesdistribution/wo_release_da/list.html')\n        self.assertIn('formset', response.context)\n        self.assertIn('staff_members_display_data', response.context)\n        \n        # Check that only active employees for comp_id=1 are in the display data\n        staff_data = response.context['staff_members_display_data']\n        self.assertEqual(len(staff_data), 2) # EMP001, EMP002\n        self.assertTrue(any(s.emp_id == 'EMP001' for s in staff_data))\n        self.assertTrue(any(s.emp_id == 'EMP002' for s in staff_data))\n        self.assertFalse(any(s.emp_id == 'EMP003' for s in staff_data)) # Excluded ERP User\n        self.assertFalse(any(s.emp_id == 'EMP004' for s in staff_data)) # Excluded Inactive User\n        self.assertFalse(any(s.emp_id == 'EMP005' for s in staff_data)) # Excluded System User\n        self.assertFalse(any(s.emp_id == 'EMP006' for s in staff_data)) # Excluded different comp_id\n\n    def test_table_partial_view_get_htmx(self):\n        \"\"\"Test HTMX GET request to the partial table view.\"\"\"\n        headers = {'HTTP_HX_REQUEST': 'true'}\n        response = self.client.get(self.table_url, **headers)\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'salesdistribution/wo_release_da/_permission_table.html')\n        self.assertContains(response, '<table id=\"employeePermissionsTable\"')\n        \n        # Verify content for EMP001 and EMP002 is present\n        self.assertContains(response, 'Mr. John Doe')\n        self.assertContains(response, 'EMP001')\n        self.assertContains(response, 'Ms. Jane Smith')\n        self.assertContains(response, 'EMP002')\n\n    def test_post_update_permissions_success(self):\n        \"\"\"Test POST request to update permissions successfully.\"\"\"\n        initial_emp1 = OfficeStaff.objects.get(emp_id='EMP001')\n        initial_emp2 = OfficeStaff.objects.get(emp_id='EMP002')\n        self.assertFalse(initial_emp1.wr) # EMP001: WR=False, DA=False\n        self.assertTrue(initial_emp2.wr)  # EMP002: WR=True, DA=False\n\n        # Prepare formset data for updating\n        form_data = {\n            'form-TOTAL_FORMS': '2', # Number of forms in the formset\n            'form-INITIAL_FORMS': '2',\n            'form-MIN_NUM_FORMS': '0',\n            'form-MAX_NUM_FORMS': '',\n            \n            # Data for EMP001: Change WR=True, DA=True\n            'form-0-emp_id': 'EMP001',\n            'form-0-wr': 'on', # Checkbox checked\n            'form-0-da': 'on',\n            \n            # Data for EMP002: Change WR=False, DA=True\n            'form-1-emp_id': 'EMP002',\n            'form-1-wr': '', # Checkbox unchecked\n            'form-1-da': 'on',\n        }\n        \n        headers = {'HTTP_HX_REQUEST': 'true'} # Indicate HTMX request\n        response = self.client.post(self.list_url, form_data, **headers)\n        \n        # Expect 204 No Content for successful HTMX post\n        self.assertEqual(response.status_code, 204)\n        self.assertIn('HX-Trigger', response.headers)\n        self.assertEqual(response.headers['HX-Trigger'], 'refreshPermissionsList')\n\n        # Verify database updates\n        updated_emp1 = OfficeStaff.objects.get(emp_id='EMP001')\n        updated_emp2 = OfficeStaff.objects.get(emp_id='EMP002')\n        self.assertTrue(updated_emp1.wr)\n        self.assertTrue(updated_emp1.da)\n        self.assertFalse(updated_emp2.wr)\n        self.assertTrue(updated_emp2.da)\n\n    def test_post_invalid_data(self):\n        \"\"\"Test POST request with invalid data.\"\"\"\n        form_data = {\n            'form-TOTAL_FORMS': '1',\n            'form-INITIAL_FORMS': '1',\n            'form-MIN_NUM_FORMS': '0',\n            'form-MAX_NUM_FORMS': '',\n            \n            # Invalid data: Missing emp_id\n            'form-0-emp_id': '', \n            'form-0-wr': 'on',\n            'form-0-da': 'on',\n        }\n        \n        headers = {'HTTP_HX_REQUEST': 'true'}\n        response = self.client.post(self.list_url, form_data, **headers)\n        \n        # Expect 200 OK for invalid formset (re-render with errors)\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'salesdistribution/wo_release_da/list.html')\n        self.assertContains(response, 'There were errors updating permissions.')\n        self.assertContains(response, 'This field is required.') # Error message for emp_id\n        \n        # Verify no database changes occurred for EMP001\n        emp1 = OfficeStaff.objects.get(emp_id='EMP001')\n        self.assertFalse(emp1.wr)\n        self.assertFalse(emp1.da)"], "admin": []}