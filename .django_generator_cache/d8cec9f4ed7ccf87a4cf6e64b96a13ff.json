{"models": ["from django.db import models\nfrom django.utils import timezone\n\nclass WoSubCategory(models.Model):\n    \"\"\"\n    Represents a Work Order Sub-Category in the system.\n    Maps to the existing tbl_WoSubCategory database table.\n    \"\"\"\n    id = models.AutoField(db_column='id', primary_key=True)\n    sub_category_name = models.CharField(\n        db_column='sub_category_name', \n        max_length=255, \n        unique=True, \n        verbose_name=\"Sub-Category Name\"\n    )\n    description = models.TextField(\n        db_column='description', \n        blank=True, \n        null=True, \n        verbose_name=\"Description\"\n    )\n    is_active = models.BooleanField(\n        db_column='is_active', \n        default=True, \n        verbose_name=\"Is Active\"\n    )\n    created_at = models.DateTimeField(\n        db_column='created_at', \n        auto_now_add=True, \n        verbose_name=\"Created At\"\n    )\n    updated_at = models.DateTimeField(\n        db_column='updated_at', \n        auto_now=True, \n        verbose_name=\"Updated At\"\n    )\n\n    class Meta:\n        managed = False  # Tells Django not to manage table creation/deletion\n        db_table = 'tbl_WoSubCategory'  # Name of the existing database table\n        verbose_name = 'Work Order Sub-Category'\n        verbose_name_plural = 'Work Order Sub-Categories'\n        ordering = ['sub_category_name'] # Default ordering for lists\n\n    def __str__(self):\n        \"\"\"String representation for a WoSubCategory object.\"\"\"\n        return self.sub_category_name\n        \n    def activate(self):\n        \"\"\"Business logic: Activates the sub-category.\"\"\"\n        if not self.is_active:\n            self.is_active = True\n            self.save(update_fields=['is_active', 'updated_at'])\n            return True\n        return False\n\n    def deactivate(self):\n        \"\"\"Business logic: Deactivates the sub-category.\"\"\"\n        if self.is_active:\n            self.is_active = False\n            self.save(update_fields=['is_active', 'updated_at'])\n            return True\n        return False\n\n    def get_display_status(self):\n        \"\"\"Returns a user-friendly status string.\"\"\"\n        return \"Active\" if self.is_active else \"Inactive\""], "views": ["from django.views.generic import List<PERSON>ie<PERSON>, <PERSON><PERSON><PERSON>iew, UpdateView, DeleteView, TemplateView\nfrom django.urls import reverse_lazy\nfrom django.contrib import messages\nfrom django.http import HttpResponse\nfrom .models import WoSubCategory\nfrom .forms import WoSubCategoryForm\n\nclass WoSubCategoryListView(ListView):\n    \"\"\"\n    Displays the dashboard for Work Order Sub-Categories.\n    Renders the main page that will load the table via HTMX.\n    \"\"\"\n    model = WoSubCategory\n    template_name = 'work_order/wosubcategory/list.html'\n    context_object_name = 'wosubcategories' # Not directly used by this view, but good practice.\n\nclass WoSubCategoryTablePartialView(ListView):\n    \"\"\"\n    Renders the partial HTML for the WoSubCategory list table.\n    Designed to be fetched via HTMX.\n    \"\"\"\n    model = WoSubCategory\n    template_name = 'work_order/wosubcategory/_wosubcategory_table.html'\n    context_object_name = 'wosubcategories'\n\nclass WoSubCategoryCreateView(CreateView):\n    \"\"\"\n    Handles creation of new Work Order Sub-Categories.\n    \"\"\"\n    model = WoSubCategory\n    form_class = WoSubCategoryForm\n    template_name = 'work_order/wosubcategory/_wosubcategory_form.html' # Use partial template for modal\n    success_url = reverse_lazy('wosubcategory_list') # Not strictly used with HTMX, but for Django fallback\n\n    def form_valid(self, form):\n        \"\"\"\n        Processes valid form submission for creation.\n        Returns a no-content response with HX-Trigger for HTMX.\n        \"\"\"\n        response = super().form_valid(form)\n        messages.success(self.request, 'Work Order Sub-Category added successfully.')\n        if self.request.headers.get('HX-Request'):\n            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshWoSubCategoryList'})\n        return response\n\nclass WoSubCategoryUpdateView(UpdateView):\n    \"\"\"\n    Handles updating existing Work Order Sub-Categories.\n    \"\"\"\n    model = WoSubCategory\n    form_class = WoSubCategoryForm\n    template_name = 'work_order/wosubcategory/_wosubcategory_form.html' # Use partial template for modal\n    context_object_name = 'wosubcategory'\n    success_url = reverse_lazy('wosubcategory_list') # Not strictly used with HTMX, but for Django fallback\n\n    def form_valid(self, form):\n        \"\"\"\n        Processes valid form submission for update.\n        Returns a no-content response with HX-Trigger for HTMX.\n        \"\"\"\n        response = super().form_valid(form)\n        messages.success(self.request, 'Work Order Sub-Category updated successfully.')\n        if self.request.headers.get('HX-Request'):\n            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshWoSubCategoryList'})\n        return response\n\nclass WoSubCategoryDeleteView(DeleteView):\n    \"\"\"\n    Handles deletion of Work Order Sub-Categories.\n    \"\"\"\n    model = WoSubCategory\n    template_name = 'work_order/wosubcategory/_wosubcategory_confirm_delete.html' # Use partial for modal\n    context_object_name = 'wosubcategory'\n    success_url = reverse_lazy('wosubcategory_list') # Not strictly used with HTMX, but for Django fallback\n\n    def delete(self, request, *args, **kwargs):\n        \"\"\"\n        Processes delete request.\n        Returns a no-content response with HX-Trigger for HTMX.\n        \"\"\"\n        response = super().delete(request, *args, **kwargs)\n        messages.success(self.request, 'Work Order Sub-Category deleted successfully.')\n        if request.headers.get('HX-Request'):\n            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshWoSubCategoryList'})\n        return response"], "forms": ["from django import forms\nfrom .models import WoSubCategory\n\nclass WoSubCategoryForm(forms.ModelForm):\n    \"\"\"\n    Form for creating and updating WoSubCategory instances.\n    \"\"\"\n    class Meta:\n        model = WoSubCategory\n        fields = ['sub_category_name', 'description', 'is_active']\n        widgets = {\n            'sub_category_name': forms.TextInput(attrs={\n                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',\n                'placeholder': 'e.g., Installation'\n            }),\n            'description': forms.Textarea(attrs={\n                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',\n                'rows': 3,\n                'placeholder': 'Provide a brief description of the sub-category'\n            }),\n            'is_active': forms.CheckboxInput(attrs={\n                'class': 'h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500'\n            }),\n        }\n        labels = {\n            'sub_category_name': 'Sub-Category Name',\n            'description': 'Description',\n            'is_active': 'Active',\n        }\n        \n    def clean_sub_category_name(self):\n        \"\"\"\n        Custom validation for sub_category_name to ensure uniqueness case-insensitively\n        and trim whitespace.\n        \"\"\"\n        sub_category_name = self.cleaned_data['sub_category_name'].strip()\n        \n        # Check for uniqueness, excluding the current instance during update\n        if self.instance.pk:\n            if WoSubCategory.objects.filter(sub_category_name__iexact=sub_category_name).exclude(pk=self.instance.pk).exists():\n                raise forms.ValidationError(\"A sub-category with this name already exists.\")\n        else:\n            if WoSubCategory.objects.filter(sub_category_name__iexact=sub_category_name).exists():\n                raise forms.ValidationError(\"A sub-category with this name already exists.\")\n        \n        return sub_category_name"], "urls": ["from django.urls import path\nfrom .views import (\n    WoSubCategoryListView, \n    WoSubCategoryTablePartialView,\n    WoSubCategoryCreateView, \n    WoSubCategoryUpdateView, \n    WoSubCategoryDeleteView\n)\n\nurlpatterns = [\n    # Main dashboard view\n    path('wosubcategories/', WoSubCategoryListView.as_view(), name='wosubcategory_list'),\n    \n    # HTMX partial for the DataTables content\n    path('wosubcategories/table/', WoSubCategoryTablePartialView.as_view(), name='wosubcategory_table'),\n    \n    # CRUD operations, loaded into modal via HTMX\n    path('wosubcategories/add/', WoSubCategoryCreateView.as_view(), name='wosubcategory_add'),\n    path('wosubcategories/edit/<int:pk>/', WoSubCategoryUpdateView.as_view(), name='wosubcategory_edit'),\n    path('wosubcategories/delete/<int:pk>/', WoSubCategoryDeleteView.as_view(), name='wosubcategory_delete'),\n]"], "templates": ["{% extends 'core/base.html' %}\n\n{% block title %}Work Order Sub-Categories{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <div class=\"flex justify-between items-center mb-6\">\n        <h2 class=\"text-2xl font-bold text-gray-800\">Work Order Sub-Categories</h2>\n        <button \n            class=\"bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105\"\n            hx-get=\"{% url 'wosubcategory_add' %}\"\n            hx-target=\"#modalContent\"\n            hx-trigger=\"click\"\n            _=\"on click add .is-active to #modal\">\n            <i class=\"fas fa-plus-circle mr-2\"></i>Add New Sub-Category\n        </button>\n    </div>\n    \n    <div id=\"wosubcategoryTable-container\"\n         hx-trigger=\"load, refreshWoSubCategoryList from:body\"\n         hx-get=\"{% url 'wosubcategory_table' %}\"\n         hx-swap=\"innerHTML\"\n         class=\"bg-white shadow-lg rounded-lg p-6\">\n        <!-- Initial loading state -->\n        <div class=\"flex flex-col items-center justify-center py-10\">\n            <div class=\"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"></div>\n            <p class=\"mt-4 text-gray-600\">Loading Work Order Sub-Categories...</p>\n        </div>\n    </div>\n    \n    <!-- Global Modal for forms and confirmations -->\n    <div id=\"modal\" class=\"fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden\"\n         _=\"on click if event.target.id == 'modal' remove .is-active from me\">\n        <div id=\"modalContent\" class=\"bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto transform scale-95 opacity-0 transition-all duration-300\"\n             _=\"on modal.active transition transform scale-100 opacity-100\">\n            <!-- Content loaded via HTMX -->\n        </div>\n    </div>\n</div>\n{% endblock %}\n\n{% block extra_js %}\n<script>\n    // Alpine.js is typically initialized globally in base.html\n    // Specific Alpine.js components can be defined here if needed for this page\n    document.addEventListener('htmx:afterSwap', function(event) {\n        // If content swapped into modalContent, ensure Alpine.js re-initializes itself.\n        // This is often handled by Alpine's own observers, but good to be aware.\n        if (event.target.id === 'modalContent') {\n            // Check if DataTable was loaded and re-initialize it\n            if ($(event.detail.target).find('.dataTable').length) {\n                $(event.detail.target).find('.dataTable').DataTable({\n                    \"pageLength\": 10,\n                    \"lengthMenu\": [[10, 25, 50, -1], [10, 25, 50, \"All\"]],\n                    \"responsive\": true\n                });\n            }\n        }\n    });\n\n    // Close modal when an HX-Trigger is received\n    document.body.addEventListener('refreshWoSubCategoryList', function() {\n        const modal = document.getElementById('modal');\n        if (modal) {\n            modal.classList.remove('is-active');\n        }\n    });\n</script>\n{% endblock %}", "<table id=\"wosubcategoryTable\" class=\"min-w-full bg-white border border-gray-200 rounded-lg shadow-sm dataTable\">\n    <thead class=\"bg-gray-100\">\n        <tr>\n            <th class=\"py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\">SN</th>\n            <th class=\"py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\">Sub-Category Name</th>\n            <th class=\"py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\">Description</th>\n            <th class=\"py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider\">Status</th>\n            <th class=\"py-3 px-4 border-b border-gray-200 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider\">Actions</th>\n        </tr>\n    </thead>\n    <tbody>\n        {% for obj in wosubcategories %}\n        <tr class=\"hover:bg-gray-50 transition-colors duration-150\">\n            <td class=\"py-3 px-4 border-b border-gray-200 text-sm text-gray-700\">{{ forloop.counter }}</td>\n            <td class=\"py-3 px-4 border-b border-gray-200 text-sm text-gray-700 font-medium\">{{ obj.sub_category_name }}</td>\n            <td class=\"py-3 px-4 border-b border-gray-200 text-sm text-gray-700\">{{ obj.description|default:\"-\" }}</td>\n            <td class=\"py-3 px-4 border-b border-gray-200 text-sm text-gray-700\">\n                <span class=\"px-2 inline-flex text-xs leading-5 font-semibold rounded-full \n                    {% if obj.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}\">\n                    {{ obj.get_display_status }}\n                </span>\n            </td>\n            <td class=\"py-3 px-4 border-b border-gray-200 text-center\">\n                <button \n                    class=\"bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1.5 px-3 rounded-lg mr-2 transition duration-150 ease-in-out\"\n                    hx-get=\"{% url 'wosubcategory_edit' obj.pk %}\"\n                    hx-target=\"#modalContent\"\n                    hx-trigger=\"click\"\n                    _=\"on click add .is-active to #modal\">\n                    <i class=\"fas fa-edit\"></i> Edit\n                </button>\n                <button \n                    class=\"bg-red-500 hover:bg-red-600 text-white font-bold py-1.5 px-3 rounded-lg transition duration-150 ease-in-out\"\n                    hx-get=\"{% url 'wosubcategory_delete' obj.pk %}\"\n                    hx-target=\"#modalContent\"\n                    hx-trigger=\"click\"\n                    _=\"on click add .is-active to #modal\">\n                    <i class=\"fas fa-trash-alt\"></i> Delete\n                </button>\n            </td>\n        </tr>\n        {% empty %}\n        <tr>\n            <td colspan=\"5\" class=\"py-4 px-4 text-center text-gray-500\">No Work Order Sub-Categories found.</td>\n        </tr>\n        {% endfor %}\n    </tbody>\n</table>\n\n<script>\n    // DataTables initialization must happen *after* the table HTML is loaded into the DOM\n    // This script block is part of the HTMX response, so it will run immediately.\n    $(document).ready(function() {\n        if (!$.fn.DataTable.isDataTable('#wosubcategoryTable')) {\n            $('#wosubcategoryTable').DataTable({\n                \"pageLength\": 10,\n                \"lengthMenu\": [[10, 25, 50, -1], [10, 25, 50, \"All\"]],\n                \"responsive\": true // Add responsiveness\n            });\n        }\n    });\n</script>", "<div class=\"p-8\">\n    <h3 class=\"text-2xl font-semibold text-gray-900 mb-6 border-b pb-3\">\n        {{ form.instance.pk|yesno:'Edit,Add' }} Work Order Sub-Category\n    </h3>\n    <form hx-post=\"{{ request.path }}\" hx-swap=\"none\" \n          hx-on--after-request=\"if(event.detail.successful) { console.log('Form submission successful'); document.getElementById('modal').classList.remove('is-active'); } else { console.error('Form submission failed'); }\">\n        {% csrf_token %}\n        \n        <div class=\"space-y-5\">\n            {% for field in form %}\n            <div class=\"mb-4\">\n                <label for=\"{{ field.id_for_label }}\" class=\"block text-sm font-medium text-gray-700 mb-1\">\n                    {{ field.label }}\n                </label>\n                {% if field.widget_type == 'checkbox' %}\n                <div class=\"flex items-center\">\n                    {{ field }}\n                    <span class=\"ml-2 text-sm text-gray-900\">{{ field.label }}</span>\n                </div>\n                {% else %}\n                    {{ field }}\n                {% endif %}\n                {% if field.errors %}\n                <p class=\"mt-2 text-sm text-red-600\">{{ field.errors }}</p>\n                {% endif %}\n                {% if field.help_text %}\n                <p class=\"mt-2 text-sm text-gray-500\">{{ field.help_text }}</p>\n                {% endif %}\n            </div>\n            {% endfor %}\n        </div>\n        \n        <div class=\"mt-8 flex items-center justify-end space-x-4\">\n            <button \n                type=\"button\" \n                class=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-lg shadow-sm transition duration-150 ease-in-out\"\n                _=\"on click remove .is-active from #modal\">\n                Cancel\n            </button>\n            <button \n                type=\"submit\" \n                class=\"bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-150 ease-in-out transform hover:scale-105\">\n                <i class=\"fas fa-save mr-2\"></i>Save Sub-Category\n            </button>\n        </div>\n    </form>\n</div>", "<div class=\"p-8 text-center\">\n    <div class=\"text-red-500 text-5xl mb-6\">\n        <i class=\"fas fa-exclamation-triangle\"></i>\n    </div>\n    <h3 class=\"text-2xl font-semibold text-gray-900 mb-4\">Confirm Deletion</h3>\n    <p class=\"text-gray-700 text-lg mb-6\">\n        Are you sure you want to delete the Work Order Sub-Category: \n        <span class=\"font-bold text-red-700\">\"{{ wosubcategory.sub_category_name }}\"</span>?\n    </p>\n    <p class=\"text-gray-500 text-sm mb-8\">This action cannot be undone.</p>\n    \n    <div class=\"flex justify-center space-x-4\">\n        <button \n            type=\"button\" \n            class=\"bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-sm transition duration-150 ease-in-out\"\n            _=\"on click remove .is-active from #modal\">\n            Cancel\n        </button>\n        <button \n            hx-post=\"{% url 'wosubcategory_delete' wosubcategory.pk %}\"\n            hx-swap=\"none\"\n            hx-on--after-request=\"if(event.detail.successful) { console.log('Deletion successful'); document.getElementById('modal').classList.remove('is-active'); }\"\n            class=\"bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-150 ease-in-out transform hover:scale-105\">\n            <i class=\"fas fa-trash-alt mr-2\"></i>Delete\n        </button>\n    </div>\n</div>"], "tests": ["from django.test import TestCase, Client\nfrom django.urls import reverse\nfrom django.contrib import messages\nfrom .models import WoSubCategory\n\nclass WoSubCategoryModelTest(TestCase):\n    \"\"\"\n    Tests for the WoSubCategory model, ensuring correct field behavior and business logic.\n    \"\"\"\n    @classmethod\n    def setUpTestData(cls):\n        # Create a test instance for use across all model tests\n        cls.subcategory1 = WoSubCategory.objects.create(\n            sub_category_name='Installation',\n            description='Installation services for new equipment.',\n            is_active=True\n        )\n        cls.subcategory2 = WoSubCategory.objects.create(\n            sub_category_name='Repair',\n            description='Repair services for existing equipment.',\n            is_active=False\n        )\n  \n    def test_wosubcategory_creation(self):\n        \"\"\"Verify WoSubCategory objects are created correctly.\"\"\"\n        self.assertEqual(self.subcategory1.sub_category_name, 'Installation')\n        self.assertEqual(self.subcategory1.description, 'Installation services for new equipment.')\n        self.assertTrue(self.subcategory1.is_active)\n        self.assertIsNotNone(self.subcategory1.created_at)\n        self.assertIsNotNone(self.subcategory1.updated_at)\n        \n    def test_sub_category_name_label(self):\n        \"\"\"Verify the verbose name for sub_category_name field.\"\"\"\n        field_label = self.subcategory1._meta.get_field('sub_category_name').verbose_name\n        self.assertEqual(field_label, 'Sub-Category Name')\n        \n    def test_str_method(self):\n        \"\"\"Verify the __str__ method returns the sub_category_name.\"\"\"\n        self.assertEqual(str(self.subcategory1), 'Installation')\n\n    def test_activate_method(self):\n        \"\"\"Test the activate business logic method.\"\"\"\n        initial_status = self.subcategory2.is_active\n        self.assertFalse(initial_status)\n        \n        activated = self.subcategory2.activate()\n        self.assertTrue(activated)\n        self.subcategory2.refresh_from_db() # Reload to get updated state\n        self.assertTrue(self.subcategory2.is_active)\n\n        # Test activating an already active subcategory\n        activated_again = self.subcategory1.activate()\n        self.assertFalse(activated_again) # Should return False as no change occurred\n\n    def test_deactivate_method(self):\n        \"\"\"Test the deactivate business logic method.\"\"\"\n        initial_status = self.subcategory1.is_active\n        self.assertTrue(initial_status)\n        \n        deactivated = self.subcategory1.deactivate()\n        self.assertTrue(deactivated)\n        self.subcategory1.refresh_from_db()\n        self.assertFalse(self.subcategory1.is_active)\n\n        # Test deactivating an already inactive subcategory\n        deactivated_again = self.subcategory2.deactivate()\n        self.assertFalse(deactivated_again) # Should return False as no change occurred\n\n    def test_get_display_status_method(self):\n        \"\"\"Test the get_display_status method.\"\"\"\n        self.assertEqual(self.subcategory1.get_display_status(), 'Inactive') # After deactivating it above\n        self.assertEqual(self.subcategory2.get_display_status(), 'Active')   # After activating it above\n\nclass WoSubCategoryViewsTest(TestCase):\n    \"\"\"\n    Tests for WoSubCategory views, covering CRUD operations and HTMX interactions.\n    \"\"\"\n    @classmethod\n    def setUpTestData(cls):\n        # Create initial test data for all tests\n        cls.subcategory = WoSubCategory.objects.create(\n            sub_category_name='Maintenance',\n            description='Routine maintenance checks.',\n            is_active=True\n        )\n    \n    def setUp(self):\n        # Set up a new client for each test method to ensure isolated requests\n        self.client = Client()\n    \n    def test_list_view(self):\n        \"\"\"Test the WoSubCategory list dashboard view.\"\"\"\n        response = self.client.get(reverse('wosubcategory_list'))\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'work_order/wosubcategory/list.html')\n        # We don't check for object presence here because the table is loaded via HTMX\n        \n    def test_table_partial_view(self):\n        \"\"\"Test the HTMX partial for the WoSubCategory table.\"\"\"\n        response = self.client.get(reverse('wosubcategory_table'), HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'work_order/wosubcategory/_wosubcategory_table.html')\n        self.assertContains(response, 'Maintenance') # Check if the object is present in the table HTML\n        self.assertContains(response, 'id=\"wosubcategoryTable\"') # Ensure DataTable element is there\n\n    def test_create_view_get(self):\n        \"\"\"Test GET request to the create form view.\"\"\"\n        response = self.client.get(reverse('wosubcategory_add'), HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'work_order/wosubcategory/_wosubcategory_form.html')\n        self.assertTrue('form' in response.context)\n        \n    def test_create_view_post_success(self):\n        \"\"\"Test successful POST request to create a new WoSubCategory.\"\"\"\n        data = {\n            'sub_category_name': 'Inspection',\n            'description': 'Safety inspection services.',\n            'is_active': True,\n        }\n        response = self.client.post(reverse('wosubcategory_add'), data, HTTP_HX_REQUEST='true')\n        \n        # Check for HTMX 204 No Content response on success\n        self.assertEqual(response.status_code, 204)\n        self.assertIn('HX-Trigger', response.headers)\n        self.assertEqual(response.headers['HX-Trigger'], 'refreshWoSubCategoryList')\n        \n        # Verify object was created in the database\n        self.assertTrue(WoSubCategory.objects.filter(sub_category_name='Inspection').exists())\n        \n        # Check for success message (messages framework requires session)\n        # client.post doesn't automatically process messages, would need a follow-up request\n        # For simplicity, we rely on the 204 status and database check for HTMX flows.\n    \n    def test_create_view_post_invalid(self):\n        \"\"\"Test POST request with invalid data for creation.\"\"\"\n        data = {\n            'sub_category_name': '', # Invalid: required field missing\n            'description': 'Invalid attempt.',\n            'is_active': True,\n        }\n        response = self.client.post(reverse('wosubcategory_add'), data, HTTP_HX_REQUEST='true')\n        \n        # HTMX will swap the form back with errors, so status is 200\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'work_order/wosubcategory/_wosubcategory_form.html')\n        self.assertContains(response, 'This field is required.') # Check for validation error message\n\n    def test_update_view_get(self):\n        \"\"\"Test GET request to the update form view.\"\"\"\n        response = self.client.get(reverse('wosubcategory_edit', args=[self.subcategory.pk]), HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'work_order/wosubcategory/_wosubcategory_form.html')\n        self.assertTrue('form' in response.context)\n        self.assertEqual(response.context['form'].instance, self.subcategory)\n        \n    def test_update_view_post_success(self):\n        \"\"\"Test successful POST request to update an existing WoSubCategory.\"\"\"\n        updated_name = 'Updated Maintenance'\n        data = {\n            'sub_category_name': updated_name,\n            'description': 'Updated routine maintenance checks.',\n            'is_active': False,\n        }\n        response = self.client.post(reverse('wosubcategory_edit', args=[self.subcategory.pk]), data, HTTP_HX_REQUEST='true')\n        \n        self.assertEqual(response.status_code, 204)\n        self.assertIn('HX-Trigger', response.headers)\n        self.assertEqual(response.headers['HX-Trigger'], 'refreshWoSubCategoryList')\n        \n        self.subcategory.refresh_from_db()\n        self.assertEqual(self.subcategory.sub_category_name, updated_name)\n        self.assertFalse(self.subcategory.is_active)\n\n    def test_update_view_post_invalid(self):\n        \"\"\"Test POST request with invalid data for update.\"\"\"\n        existing_subcategory = WoSubCategory.objects.create(sub_category_name='Existing Sub', description='...', is_active=True)\n        \n        data = {\n            'sub_category_name': self.subcategory.sub_category_name, # Duplicate name\n            'description': 'Attempt to create duplicate.',\n            'is_active': True,\n        }\n        response = self.client.post(reverse('wosubcategory_edit', args=[existing_subcategory.pk]), data, HTTP_HX_REQUEST='true')\n        \n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'work_order/wosubcategory/_wosubcategory_form.html')\n        self.assertContains(response, 'A sub-category with this name already exists.')\n\n    def test_delete_view_get(self):\n        \"\"\"Test GET request to the delete confirmation view.\"\"\"\n        response = self.client.get(reverse('wosubcategory_delete', args=[self.subcategory.pk]), HTTP_HX_REQUEST='true')\n        self.assertEqual(response.status_code, 200)\n        self.assertTemplateUsed(response, 'work_order/wosubcategory/_wosubcategory_confirm_delete.html')\n        self.assertTrue('wosubcategory' in response.context)\n        self.assertEqual(response.context['wosubcategory'], self.subcategory)\n        \n    def test_delete_view_post_success(self):\n        \"\"\"Test successful POST request to delete a WoSubCategory.\"\"\"\n        pk_to_delete = self.subcategory.pk # Store PK before deletion\n        response = self.client.post(reverse('wosubcategory_delete', args=[pk_to_delete]), HTTP_HX_REQUEST='true')\n        \n        self.assertEqual(response.status_code, 204)\n        self.assertIn('HX-Trigger', response.headers)\n        self.assertEqual(response.headers['HX-Trigger'], 'refreshWoSubCategoryList')\n        \n        # Verify object was deleted from the database\n        self.assertFalse(WoSubCategory.objects.filter(pk=pk_to_delete).exists())\n\n    def test_delete_view_post_non_existent(self):\n        \"\"\"Test POST request to delete a non-existent WoSubCategory.\"\"\"\n        response = self.client.post(reverse('wosubcategory_delete', args=[9999]), HTTP_HX_REQUEST='true')\n        # Expect 404 since the object doesn't exist\n        self.assertEqual(response.status_code, 404)"], "admin": []}