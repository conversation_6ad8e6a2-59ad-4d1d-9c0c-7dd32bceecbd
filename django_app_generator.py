#!/usr/bin/env python3
"""
Django App Generator from Documentation using Gemini 2.5 Flash Preview

This revolutionary tool leverages Google's Gemini 2.5 Flash Preview model to automatically
extract Django code from markdown documentation and create organized Django applications.

Features:
- AI-powered code extraction using Gemini 2.5 Flash Preview with thinking capabilities
- Automated Django app creation with proper file organization
- Pattern-based code classification and separation
- Settings.py integration and dependency management
- Code execution validation and quality assurance

Usage:
    python django_app_generator.py --scan-docs --generate-apps
    python django_app_generator.py --app sales_distribution --extract-only
    python django_app_generator.py --validate-generated-code
"""

import os
import sys
import re
import json
import subprocess
import shutil
import hashlib
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import argparse

# Google GenAI imports
from google import genai
from google.genai.types import (
    GenerateContentConfig,
    ThinkingConfig,
    Tool,
    ToolCodeExecution,
    SafetySetting,
    HarmCategory,
    HarmBlockThreshold,
    Part,
)

# Environment setup
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("django_generator.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


class DjangoAppGenerator:
    """
    Revolutionary Django App Generator using Gemini 2.5 Flash Preview

    This class implements the world's first bidirectional AI code transformation platform
    that can extract Django applications from structured documentation.
    """

    def __init__(self, project_root: str = "."):
        """Initialize the Django App Generator"""
        self.project_root = Path(project_root).resolve()
        self.docs_dir = self.project_root / "docs"
        self.manage_py = self.project_root / "manage.py"
        self.settings_file = self.project_root / "autoerp" / "settings.py"

        # Initialize Gemini 2.5 Flash Preview client
        self.api_key = os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY environment variable not set")

        self.client = genai.Client(api_key=self.api_key)
        self.model_id = os.getenv("MODEL_ID", "gemini-2.5-flash-preview-05-20")

        # Configure thinking for complex code analysis - OPTIMIZED FOR ENTERPRISE
        self.thinking_config = ThinkingConfig(
            thinking_budget=8192,  # MAXIMUM thinking budget for complex enterprise code analysis
            include_thoughts=True,  # Include reasoning process for debugging
        )

        # Alternative: Dynamic thinking (no budget limit) - use when needed
        self.dynamic_thinking_config = ThinkingConfig(
            # No thinking_budget specified = dynamic thinking (unlimited)
            include_thoughts=True
        )

        # High-performance config for large files
        self.max_thinking_config = ThinkingConfig(
            thinking_budget=24576,  # Maximum possible budget for very complex analysis
            include_thoughts=True,
        )

        # Code execution tool for validation
        self.code_execution_tool = Tool(code_execution=ToolCodeExecution())

        # Cache for processed files
        self.cache_dir = self.project_root / ".django_generator_cache"
        self.cache_dir.mkdir(exist_ok=True)

        # Validate project structure
        self._validate_project_structure()

        logger.info(f"Django App Generator initialized with Gemini {self.model_id}")
        logger.info(f"Project root: {self.project_root}")
        logger.info(f"Documentation directory: {self.docs_dir}")
        logger.info(f"Thinking budget: {self.thinking_config.thinking_budget} tokens")
        logger.info(f"Dynamic thinking available: {self.dynamic_thinking_config}")

    def configure_thinking_budget(
        self, budget: Optional[int] = None, dynamic: bool = False
    ):
        """
        Configure thinking budget for different use cases

        Args:
            budget: Token budget (None for dynamic, 8192 default, 24576 max)
            dynamic: Use dynamic thinking (unlimited budget)
        """
        if dynamic:
            self.thinking_config = ThinkingConfig(include_thoughts=True)
            logger.info("🧠 Configured for DYNAMIC thinking (unlimited budget)")
        elif budget is None:
            # Use default optimized budget
            self.thinking_config = ThinkingConfig(
                thinking_budget=8192, include_thoughts=True
            )
            logger.info("🧠 Configured for STANDARD thinking (8192 tokens)")
        elif budget <= 24576:
            self.thinking_config = ThinkingConfig(
                thinking_budget=budget, include_thoughts=True
            )
            logger.info(f"🧠 Configured for CUSTOM thinking ({budget} tokens)")
        else:
            logger.warning(f"Budget {budget} exceeds maximum, using 24576")
            self.thinking_config = ThinkingConfig(
                thinking_budget=24576, include_thoughts=True
            )

    def get_optimal_config_for_content(
        self, content_size: int
    ) -> GenerateContentConfig:
        """
        Get optimal configuration based on content size and complexity
        """
        if content_size < 5000:
            # Small content - standard thinking
            thinking_config = ThinkingConfig(
                thinking_budget=4096, include_thoughts=True
            )
            max_output = 8192
        elif content_size < 20000:
            # Medium content - enhanced thinking
            thinking_config = ThinkingConfig(
                thinking_budget=8192, include_thoughts=True
            )
            max_output = 16384
        elif content_size < 50000:
            # Large content - maximum thinking
            thinking_config = ThinkingConfig(
                thinking_budget=24576, include_thoughts=True
            )
            max_output = 32768
        else:
            # Very large content - dynamic thinking (unlimited)
            thinking_config = ThinkingConfig(include_thoughts=True)
            max_output = 65536  # Maximum possible output

        return GenerateContentConfig(
            thinking_config=thinking_config,
            response_mime_type="application/json",
            temperature=0.05,  # Very low for maximum accuracy
            max_output_tokens=max_output,
            # Note: Tools not supported with JSON response format
        )

    def _validate_project_structure(self):
        """Validate Django project structure"""
        if not self.manage_py.exists():
            raise FileNotFoundError(f"manage.py not found at {self.manage_py}")
        if not self.settings_file.exists():
            raise FileNotFoundError(f"settings.py not found at {self.settings_file}")
        if not self.docs_dir.exists():
            raise FileNotFoundError(f"docs directory not found at {self.docs_dir}")

        logger.info("✅ Project structure validation passed")

    def discover_apps(self) -> List[str]:
        """
        Discover potential Django apps from docs directory structure using AI analysis
        """
        logger.info("🔍 Discovering Django apps from documentation structure...")

        # Get directory structure
        doc_structure = self._get_directory_structure(self.docs_dir)

        # Use Gemini to analyze and suggest Django apps with MAXIMUM thinking power
        prompt = f"""
        Analyze this documentation directory structure and identify potential Django applications:

        Directory Structure:
        {doc_structure}

        Based on Django best practices and the documentation organization, suggest:
        1. Which top-level directories should become Django apps
        2. How to organize related functionality into logical Django apps
        3. Skip small utility apps like 'scheduler', 'sys_admin', 'mr_office'
        4. Consider business domain separation (sales, inventory, hr, etc.)
        5. Analyze the complexity and relationships between different modules
        6. Consider enterprise-scale architecture patterns

        Take your time to think through this thoroughly. Return a JSON list of recommended Django app names with detailed descriptions and reasoning.
        """

        response = self.client.models.generate_content(
            model=self.model_id,
            contents=prompt,
            config=GenerateContentConfig(
                thinking_config=self.dynamic_thinking_config,  # Use unlimited thinking
                response_mime_type="application/json",
                temperature=0.1,  # Low temperature for consistent results
                max_output_tokens=8192,  # Increased output limit
            ),
        )

        try:
            apps_data = json.loads(response.text)
            apps = [app["name"] for app in apps_data if "name" in app]

            logger.info(f"✅ Discovered {len(apps)} potential Django apps: {apps}")
            return apps
        except json.JSONDecodeError:
            # Fallback to directory-based discovery
            logger.warning(
                "AI analysis failed, falling back to directory-based discovery"
            )
            return self._fallback_app_discovery()

    def _get_directory_structure(
        self, path: Path, max_depth: int = 3, current_depth: int = 0
    ) -> str:
        """Get directory structure as a string"""
        if current_depth >= max_depth:
            return ""

        structure = []
        try:
            for item in sorted(path.iterdir()):
                if item.is_dir() and not item.name.startswith("."):
                    indent = "  " * current_depth
                    structure.append(f"{indent}{item.name}/")
                    if current_depth < max_depth - 1:
                        sub_structure = self._get_directory_structure(
                            item, max_depth, current_depth + 1
                        )
                        if sub_structure:
                            structure.append(sub_structure)
        except PermissionError:
            pass

        return "\n".join(structure)

    def _fallback_app_discovery(self) -> List[str]:
        """Fallback method for app discovery"""
        apps = []
        skip_apps = ["scheduler", "sys_admin", "mr_office", "reports"]

        for item in self.docs_dir.iterdir():
            if item.is_dir() and not item.name.startswith("."):
                if item.name not in skip_apps:
                    apps.append(item.name)

        return apps

    def extract_django_components(self, app_name: str) -> Dict[str, List[str]]:
        """
        Extract Django components from documentation using Gemini 2.5 Flash Preview
        """
        logger.info(f"🔍 Extracting Django components for app: {app_name}")

        app_docs_dir = self.docs_dir / app_name
        if not app_docs_dir.exists():
            logger.error(f"Documentation directory not found: {app_docs_dir}")
            return {}

        # Collect all markdown files
        markdown_files = list(app_docs_dir.rglob("*.md"))
        logger.info(f"Found {len(markdown_files)} markdown files")

        # Process files in batches to avoid token limits
        all_components = {
            "models": [],
            "views": [],
            "forms": [],
            "urls": [],
            "templates": [],
            "tests": [],
            "admin": [],
        }

        for md_file in markdown_files:
            logger.info(f"Processing: {md_file.relative_to(self.docs_dir)}")

            # Check cache first
            cache_key = self._get_cache_key(md_file)
            cached_result = self._get_cached_result(cache_key)

            if cached_result:
                logger.info(f"Using cached result for {md_file.name}")
                components = cached_result
            else:
                components = self._extract_from_file(md_file, app_name)
                self._cache_result(cache_key, components)

            # Merge components
            for component_type, code_blocks in components.items():
                if component_type in all_components:
                    all_components[component_type].extend(code_blocks)

        # Log extraction summary
        total_extracted = sum(len(blocks) for blocks in all_components.values())
        logger.info(f"✅ Extracted {total_extracted} code blocks for {app_name}")

        for component_type, blocks in all_components.items():
            if blocks:
                logger.info(f"  - {component_type}: {len(blocks)} blocks")

        return all_components

    def _extract_from_file(self, md_file: Path, app_name: str) -> Dict[str, List[str]]:
        """Extract Django components from a single markdown file using AI"""
        try:
            content = md_file.read_text(encoding="utf-8")
        except Exception as e:
            logger.error(f"Failed to read {md_file}: {e}")
            return {}

        # Determine content size and choose appropriate processing strategy
        content_size = len(content)
        if content_size > 50000:  # Large file
            # Process in chunks for very large files
            return self._process_large_file(md_file, app_name, content)

        # For normal files, use maximum thinking power
        prompt = f"""
        You are the world's most advanced Django code extraction AI with unlimited thinking power.

        MISSION: Extract Django components from this enterprise documentation with PERFECT accuracy.

        CONTEXT:
        - File: {md_file.name}
        - Django App: {app_name}
        - Content Size: {content_size} characters
        - This is enterprise-grade Django application documentation

        DOCUMENTATION CONTENT:
        {content}

        EXTRACTION REQUIREMENTS:

        1. **MODELS** - Extract complete Django model classes:
           - Full class definitions with all fields
           - Field types (CharField, ForeignKey, etc.) with all parameters
           - Meta classes with db_table, ordering, verbose_name
           - Custom methods, properties, __str__ methods
           - Business logic methods
           - All imports (from django.db import models, etc.)

        2. **VIEWS** - Extract complete Django views:
           - Class-based views (ListView, CreateView, UpdateView, DeleteView)
           - Function-based views with request parameters
           - HTMX-specific response handling
           - Mixins and custom view logic
           - All imports (from django.views.generic, etc.)

        3. **FORMS** - Extract complete Django forms:
           - ModelForm classes with Meta definitions
           - Field widgets with Tailwind CSS classes
           - Custom validation methods (clean_*)
           - Form field customizations
           - All imports (from django import forms, etc.)

        4. **URLS** - Extract complete URL configurations:
           - urlpatterns list with all path() definitions
           - View imports and app_name declarations
           - URL namespaces and includes
           - All imports (from django.urls import path, etc.)

        5. **TEMPLATES** - Extract complete HTML templates:
           - Full template structure with extends/blocks
           - Django template tags and filters
           - HTMX attributes (hx-get, hx-target, etc.)
           - Alpine.js directives (x-data, x-show, etc.)
           - Tailwind CSS classes

        6. **ADMIN** - Extract Django admin configurations
        7. **TESTS** - Extract Django test cases

        CRITICAL SUCCESS FACTORS:
        - Extract COMPLETE, FUNCTIONAL code blocks (not fragments)
        - Maintain EXACT indentation and formatting
        - Include ALL necessary imports
        - Preserve ALL comments and docstrings
        - Each code block must be syntactically correct Python/HTML
        - Do NOT truncate or summarize any code

        RESPONSE FORMAT:
        Return ONLY a valid JSON object with this exact structure:
        {{
            "models": ["complete model code 1", "complete model code 2", ...],
            "views": ["complete view code 1", "complete view code 2", ...],
            "forms": ["complete form code 1", "complete form code 2", ...],
            "urls": ["complete url code 1", "complete url code 2", ...],
            "templates": ["complete template 1", "complete template 2", ...],
            "admin": ["complete admin code 1", ...],
            "tests": ["complete test code 1", ...]
        }}

        Use your MAXIMUM thinking power to analyze this documentation thoroughly and extract every piece of Django code with perfect accuracy.
        """

        # Use optimal configuration based on content size and complexity
        optimal_config = self.get_optimal_config_for_content(content_size)

        logger.info(
            f"Using optimal config for {content_size} chars: "
            f"thinking_budget={getattr(optimal_config.thinking_config, 'thinking_budget', 'dynamic')}, "
            f"max_output={optimal_config.max_output_tokens}"
        )

        response = self.client.models.generate_content(
            model=self.model_id,
            contents=prompt,
            config=optimal_config,
        )

        try:
            # Clean the response text to extract JSON
            response_text = response.text.strip()

            # Try to find JSON in the response
            json_start = response_text.find("{")
            json_end = response_text.rfind("}") + 1

            if json_start != -1 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                extracted_components = json.loads(json_text)
            else:
                # Fallback: try parsing the entire response
                extracted_components = json.loads(response_text)

            # Validate and clean the extracted components
            cleaned_components = self._validate_extracted_code(extracted_components)

            logger.info(f"✅ Successfully extracted components from {md_file.name}")
            return cleaned_components

        except json.JSONDecodeError as e:
            logger.error(f"❌ Failed to parse JSON response for {md_file.name}: {e}")
            logger.error(f"Response preview: {response.text[:500]}...")

            # Try to extract code blocks manually as fallback
            logger.info("🔄 Attempting manual code block extraction as fallback...")
            return self._fallback_code_extraction(content)

        except Exception as e:
            logger.error(f"❌ Unexpected error processing {md_file.name}: {e}")
            return self._fallback_code_extraction(content)

    def _process_large_file(
        self, md_file: Path, app_name: str, content: str
    ) -> Dict[str, List[str]]:
        """Process very large files by chunking them intelligently"""
        logger.info(
            f"Processing large file: {md_file.name} ({len(content)} characters)"
        )

        # Split content into logical chunks (by sections, code blocks, etc.)
        chunks = self._split_content_intelligently(content)

        all_components = {
            "models": [],
            "views": [],
            "forms": [],
            "urls": [],
            "templates": [],
            "tests": [],
            "admin": [],
        }

        for i, chunk in enumerate(chunks):
            logger.info(
                f"Processing chunk {i+1}/{len(chunks)} ({len(chunk)} characters)"
            )

            prompt = f"""
            Analyze this section of a large Django documentation file and extract code components:

            File: {md_file.name} (Chunk {i+1}/{len(chunks)})
            App: {app_name}

            Content Section:
            {chunk}

            Extract Django code components with maximum detail. Focus on:
            1. Complete code blocks with proper formatting
            2. All Django components (models, views, forms, URLs, templates, tests, admin)
            3. Preserve relationships and dependencies
            4. Include imports and business logic

            Return JSON with extracted components.
            """

            try:
                response = self.client.models.generate_content(
                    model=self.model_id,
                    contents=prompt,
                    config=GenerateContentConfig(
                        thinking_config=self.max_thinking_config,  # Maximum thinking for complex chunks
                        response_mime_type="application/json",
                        temperature=0.05,
                        max_output_tokens=16384,  # Large output for detailed extraction
                        # Note: Tools not supported with JSON response format
                    ),
                )

                chunk_components = json.loads(response.text)
                cleaned_components = self._validate_extracted_code(chunk_components)

                # Merge with all components
                for component_type, code_blocks in cleaned_components.items():
                    if component_type in all_components:
                        all_components[component_type].extend(code_blocks)

            except Exception as e:
                logger.error(f"Failed to process chunk {i+1}: {e}")
                # Continue with other chunks
                continue

        logger.info(
            f"Large file processing complete: {sum(len(blocks) for blocks in all_components.values())} total components"
        )
        return all_components

    def _split_content_intelligently(
        self, content: str, max_chunk_size: int = 30000
    ) -> List[str]:
        """Split content into intelligent chunks based on markdown structure"""
        chunks = []

        # Split by major sections first (## headers)
        sections = re.split(r"\n## ", content)

        current_chunk = ""

        for section in sections:
            # Add back the header marker (except for first section)
            if section != sections[0]:
                section = "## " + section

            # If adding this section would exceed chunk size, save current chunk
            if len(current_chunk) + len(section) > max_chunk_size and current_chunk:
                chunks.append(current_chunk.strip())
                current_chunk = section
            else:
                current_chunk += "\n" + section if current_chunk else section

        # Add the last chunk
        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        # If we still have chunks that are too large, split them further
        final_chunks = []
        for chunk in chunks:
            if len(chunk) > max_chunk_size:
                # Split by code blocks
                sub_chunks = self._split_by_code_blocks(chunk, max_chunk_size)
                final_chunks.extend(sub_chunks)
            else:
                final_chunks.append(chunk)

        logger.info(f"Split content into {len(final_chunks)} intelligent chunks")
        return final_chunks

    def _split_by_code_blocks(self, content: str, max_size: int) -> List[str]:
        """Split content by code blocks if sections are still too large"""
        chunks = []
        current_chunk = ""

        # Split by code block boundaries
        parts = re.split(r"(```[\s\S]*?```)", content)

        for part in parts:
            if len(current_chunk) + len(part) > max_size and current_chunk:
                chunks.append(current_chunk.strip())
                current_chunk = part
            else:
                current_chunk += part

        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        return chunks

    def _classify_python_code(self, code: str) -> Optional[str]:
        """
        Classify Python code block by analyzing its content.
        """
        code_lower = code.lower()

        # Check for Django patterns
        if "class.*models.model" in re.sub(r"\s+", " ", code_lower):
            return "models"
        elif any(
            pattern in code_lower
            for pattern in [
                "class.*view",
                "def get(",
                "def post(",
                "templateview",
                "listview",
            ]
        ):
            return "views"
        elif any(
            pattern in code_lower
            for pattern in ["class.*form", "forms.form", "forms.modelform"]
        ):
            return "forms"
        elif "urlpatterns" in code_lower or "path(" in code_lower:
            return "urls"
        elif any(
            pattern in code_lower
            for pattern in ["class.*test", "def test_", "testcase"]
        ):
            return "tests"
        elif "admin.site.register" in code_lower or "class.*admin" in code_lower:
            return "admin"

        return None

    def create_django_app(self, app_name: str) -> bool:
        """
        Create Django app using manage.py startapp command.
        """
        app_path = self.project_root / app_name

        if app_path.exists():
            logger.warning(
                f"App directory {app_name} already exists, skipping creation"
            )
            return True

        try:
            cmd = [sys.executable, str(self.manage_py), "startapp", app_name]
            result = subprocess.run(
                cmd, cwd=self.project_root, capture_output=True, text=True
            )

            if result.returncode == 0:
                logger.info(f"Successfully created Django app: {app_name}")
                return True
            else:
                logger.error(f"Failed to create app {app_name}: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"Error creating app {app_name}: {e}")
            return False

    def create_organized_structure(self, app_name: str):
        """
        Create organized directory structure within the Django app.
        """
        app_path = self.project_root / app_name

        # Create subdirectories for organized code
        subdirs = [
            "models",
            "views",
            "forms",
            "urls",
            Path("templates") / app_name,
            Path("static") / app_name / "css",
            Path("static") / app_name / "js",
            "admin",
            "tests",
        ]

        for subdir in subdirs:
            dir_path = app_path / subdir
            dir_path.mkdir(parents=True, exist_ok=True)

            # Create __init__.py files for Python packages
            if not str(subdir).startswith(("templates", "static")):
                init_file = dir_path / "__init__.py"
                if not init_file.exists():
                    init_file.touch()

        logger.info(f"Created organized structure for app: {app_name}")

    def _get_cache_key(self, file_path: Path) -> str:
        """Generate cache key for a file"""
        content = file_path.read_text(encoding="utf-8")
        return hashlib.md5(content.encode()).hexdigest()

    def _get_cached_result(self, cache_key: str) -> Optional[Dict]:
        """Get cached extraction result"""
        cache_file = self.cache_dir / f"{cache_key}.json"
        if cache_file.exists():
            try:
                return json.loads(cache_file.read_text())
            except Exception:
                pass
        return None

    def _cache_result(self, cache_key: str, result: Dict):
        """Cache extraction result"""
        cache_file = self.cache_dir / f"{cache_key}.json"
        try:
            cache_file.write_text(json.dumps(result, indent=2))
        except Exception as e:
            logger.warning(f"Failed to cache result: {e}")

    def _validate_extracted_code(self, components) -> Dict[str, List[str]]:
        """Validate and clean extracted code components"""
        cleaned = {
            "models": [],
            "views": [],
            "forms": [],
            "urls": [],
            "templates": [],
            "tests": [],
            "admin": [],
        }

        # Handle case where AI returns a list instead of dict
        if isinstance(components, list):
            logger.warning("AI returned list instead of dict, skipping this chunk")
            return cleaned

        # Handle case where components is None
        if not components or not isinstance(components, dict):
            logger.warning("AI returned invalid format, skipping this chunk")
            return cleaned

        for component_type, code_blocks in components.items():
            if component_type.lower() in cleaned and isinstance(code_blocks, list):
                for code in code_blocks:
                    if isinstance(code, str) and code.strip():
                        cleaned[component_type.lower()].append(code.strip())

        return cleaned

    def _fallback_code_extraction(self, content: str) -> Dict[str, List[str]]:
        """Fallback regex-based code extraction"""
        code_blocks = {
            "models": [],
            "views": [],
            "forms": [],
            "urls": [],
            "templates": [],
            "tests": [],
            "admin": [],
        }

        # Pattern to match code blocks
        pattern = r"```(\w+)?\n(.*?)\n```"
        matches = re.findall(pattern, content, re.DOTALL)

        for lang, code in matches:
            if lang and lang.lower() == "python":
                code_type = self._classify_python_code(code)
                if code_type:
                    code_blocks[code_type].append(code.strip())
            elif lang and lang.lower() == "html":
                code_blocks["templates"].append(code.strip())

        return code_blocks

    def update_settings(self, app_names: List[str]):
        """Update Django settings.py with new apps"""
        logger.info(f"📝 Updating settings.py with {len(app_names)} apps")

        try:
            settings_content = self.settings_file.read_text()

            # Find INSTALLED_APPS
            pattern = r"INSTALLED_APPS\s*=\s*\[(.*?)\]"
            match = re.search(pattern, settings_content, re.DOTALL)

            if match:
                current_apps = match.group(1)

                # Add new apps
                new_apps_str = ""
                for app_name in app_names:
                    if (
                        f"'{app_name}'" not in current_apps
                        and f'"{app_name}"' not in current_apps
                    ):
                        new_apps_str += f"    '{app_name}',\n"

                if new_apps_str:
                    # Insert before the closing bracket
                    updated_apps = current_apps.rstrip() + "\n" + new_apps_str
                    new_settings = settings_content.replace(
                        f"INSTALLED_APPS = [{current_apps}]",
                        f"INSTALLED_APPS = [{updated_apps}]",
                    )

                    self.settings_file.write_text(new_settings)
                    logger.info(f"✅ Updated settings.py with apps: {app_names}")
                else:
                    logger.info("ℹ️ All apps already in settings.py")
            else:
                logger.error("Could not find INSTALLED_APPS in settings.py")

        except Exception as e:
            logger.error(f"Failed to update settings.py: {e}")

    def generate_app_files(self, app_name: str, components: Dict[str, List[str]]):
        """Generate Django app files from extracted components"""
        logger.info(f"📁 Generating files for app: {app_name}")

        app_path = self.project_root / app_name

        # Generate each component type
        for component_type, code_blocks in components.items():
            if code_blocks:
                self._generate_component_files(
                    app_path, component_type, code_blocks, app_name
                )

        logger.info(f"✅ Generated files for app: {app_name}")

    def _generate_component_files(
        self, app_path: Path, component_type: str, code_blocks: List[str], app_name: str
    ):
        """Generate files for a specific component type"""
        if component_type == "models":
            self._write_models(app_path, code_blocks)
        elif component_type == "views":
            self._write_views(app_path, code_blocks)
        elif component_type == "forms":
            self._write_forms(app_path, code_blocks)
        elif component_type == "urls":
            self._write_urls(app_path, code_blocks, app_name)
        elif component_type == "templates":
            self._write_templates(app_path, code_blocks, app_name)
        elif component_type == "admin":
            self._write_admin(app_path, code_blocks)
        elif component_type == "tests":
            self._write_tests(app_path, code_blocks)

    def _write_models(self, app_path: Path, code_blocks: List[str]):
        """Write model files"""
        models_dir = app_path / "models"
        models_dir.mkdir(exist_ok=True)

        # Combine all model code
        all_models = "\n\n".join(code_blocks)

        # Write to models/__init__.py
        init_file = models_dir / "__init__.py"
        init_file.write_text(f"from django.db import models\n\n{all_models}\n")

        logger.info(f"  ✅ Generated models with {len(code_blocks)} components")

    def _write_views(self, app_path: Path, code_blocks: List[str]):
        """Write view files"""
        views_dir = app_path / "views"
        views_dir.mkdir(exist_ok=True)

        # Combine all view code
        all_views = "\n\n".join(code_blocks)

        # Write to views/__init__.py
        init_file = views_dir / "__init__.py"
        init_file.write_text(
            f"from django.shortcuts import render\nfrom django.views.generic import *\n\n{all_views}\n"
        )

        logger.info(f"  ✅ Generated views with {len(code_blocks)} components")

    def _write_forms(self, app_path: Path, code_blocks: List[str]):
        """Write form files"""
        forms_dir = app_path / "forms"
        forms_dir.mkdir(exist_ok=True)

        # Combine all form code
        all_forms = "\n\n".join(code_blocks)

        # Write to forms/__init__.py
        init_file = forms_dir / "__init__.py"
        init_file.write_text(f"from django import forms\n\n{all_forms}\n")

        logger.info(f"  ✅ Generated forms with {len(code_blocks)} components")

    def _write_urls(self, app_path: Path, code_blocks: List[str], app_name: str):
        """Write URL files"""
        urls_dir = app_path / "urls"
        urls_dir.mkdir(exist_ok=True)

        # Combine all URL code
        all_urls = "\n\n".join(code_blocks)

        # Write to urls/__init__.py
        init_file = urls_dir / "__init__.py"
        init_file.write_text(
            f"from django.urls import path, include\nfrom . import views\n\napp_name = '{app_name}'\n\n{all_urls}\n"
        )

        logger.info(f"  ✅ Generated URLs with {len(code_blocks)} components")

    def _write_templates(self, app_path: Path, code_blocks: List[str], app_name: str):
        """Write template files"""
        templates_dir = app_path / "templates" / app_name
        templates_dir.mkdir(parents=True, exist_ok=True)

        # Write each template as a separate file
        for i, template_code in enumerate(code_blocks):
            template_file = templates_dir / f"template_{i+1}.html"
            template_file.write_text(template_code)

        logger.info(f"  ✅ Generated {len(code_blocks)} template files")

    def _write_admin(self, app_path: Path, code_blocks: List[str]):
        """Write admin files"""
        admin_dir = app_path / "admin"
        admin_dir.mkdir(exist_ok=True)

        # Combine all admin code
        all_admin = "\n\n".join(code_blocks)

        # Write to admin/__init__.py
        init_file = admin_dir / "__init__.py"
        init_file.write_text(
            f"from django.contrib import admin\nfrom ..models import *\n\n{all_admin}\n"
        )

        logger.info(f"  ✅ Generated admin with {len(code_blocks)} components")

    def _write_tests(self, app_path: Path, code_blocks: List[str]):
        """Write test files"""
        tests_dir = app_path / "tests"
        tests_dir.mkdir(exist_ok=True)

        # Combine all test code
        all_tests = "\n\n".join(code_blocks)

        # Write to tests/__init__.py
        init_file = tests_dir / "__init__.py"
        init_file.write_text(f"from django.test import TestCase\n\n{all_tests}\n")

        logger.info(f"  ✅ Generated tests with {len(code_blocks)} components")

    def process_all_apps(self) -> Dict[str, bool]:
        """Process all discovered apps"""
        logger.info("🚀 Starting Django app generation process...")

        # Discover apps
        apps = self.discover_apps()

        if not apps:
            logger.warning("No apps discovered from documentation")
            return {}

        results = {}
        generated_apps = []

        for app_name in apps:
            logger.info(f"\n📦 Processing app: {app_name}")

            try:
                # Create Django app
                if self.create_django_app(app_name):
                    # Create organized structure
                    self.create_organized_structure(app_name)

                    # Extract components
                    components = self.extract_django_components(app_name)

                    if components:
                        # Generate files
                        self.generate_app_files(app_name, components)
                        generated_apps.append(app_name)
                        results[app_name] = True
                        logger.info(f"✅ Successfully processed app: {app_name}")
                    else:
                        logger.warning(f"⚠️ No components extracted for app: {app_name}")
                        results[app_name] = False
                else:
                    logger.error(f"❌ Failed to create app: {app_name}")
                    results[app_name] = False

            except Exception as e:
                logger.error(f"❌ Error processing app {app_name}: {e}")
                results[app_name] = False

        # Update settings.py with successfully generated apps
        if generated_apps:
            self.update_settings(generated_apps)

        # Summary
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        logger.info(f"\n🎉 Generation complete: {successful}/{total} apps successful")

        return results


def main():
    """Main execution function with command-line interface"""
    parser = argparse.ArgumentParser(
        description="Django App Generator using Gemini 2.5 Flash Preview",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python django_app_generator.py --scan-docs --generate-apps
  python django_app_generator.py --app sales_distribution --extract-only
  python django_app_generator.py --validate-generated-code
  python django_app_generator.py --list-apps
        """,
    )

    parser.add_argument(
        "--scan-docs",
        action="store_true",
        help="Scan documentation directory for potential Django apps",
    )

    parser.add_argument(
        "--generate-apps",
        action="store_true",
        help="Generate Django apps from documentation",
    )

    parser.add_argument("--app", type=str, help="Process specific app only")

    parser.add_argument(
        "--extract-only",
        action="store_true",
        help="Extract components without creating Django apps",
    )

    parser.add_argument(
        "--list-apps",
        action="store_true",
        help="List discovered apps without processing",
    )

    parser.add_argument(
        "--validate-generated-code",
        action="store_true",
        help="Validate generated Django code using AI",
    )

    parser.add_argument(
        "--project-root",
        type=str,
        default=".",
        help="Django project root directory (default: current directory)",
    )

    parser.add_argument(
        "--thinking-budget",
        type=int,
        help="Set thinking budget (tokens): 4096, 8192, 16384, 24576, or 0 for dynamic (unlimited)",
    )

    parser.add_argument(
        "--dynamic-thinking",
        action="store_true",
        help="Use dynamic thinking (unlimited budget) for maximum accuracy",
    )

    parser.add_argument(
        "--max-output-tokens",
        type=int,
        default=32768,
        help="Maximum output tokens (default: 32768, max: 65536)",
    )

    parser.add_argument(
        "--temperature",
        type=float,
        default=0.05,
        help="Temperature for AI generation (0.0-1.0, default: 0.05 for accuracy)",
    )

    args = parser.parse_args()

    try:
        # Initialize generator
        generator = DjangoAppGenerator(args.project_root)

        # Configure thinking budget based on user preferences
        if args.dynamic_thinking:
            generator.configure_thinking_budget(dynamic=True)
            print("🧠 Using DYNAMIC thinking (unlimited budget) for maximum accuracy")
        elif args.thinking_budget is not None:
            if args.thinking_budget == 0:
                generator.configure_thinking_budget(dynamic=True)
                print("🧠 Using DYNAMIC thinking (unlimited budget)")
            else:
                generator.configure_thinking_budget(budget=args.thinking_budget)
                print(f"🧠 Using CUSTOM thinking budget: {args.thinking_budget} tokens")
        else:
            print("🧠 Using ADAPTIVE thinking budget based on content size")

        # Override default temperature if specified
        if hasattr(args, "temperature") and args.temperature != 0.05:
            print(f"🌡️ Using custom temperature: {args.temperature}")

        # Override max output tokens if specified
        if args.max_output_tokens != 32768:
            print(f"📝 Using custom max output tokens: {args.max_output_tokens}")

        if args.list_apps or args.scan_docs:
            # Discover and list apps
            apps = generator.discover_apps()
            print(f"\n🔍 Discovered {len(apps)} potential Django apps:")
            for app in apps:
                print(f"  - {app}")

            if not args.generate_apps:
                return

        if args.app:
            # Process single app
            logger.info(f"Processing single app: {args.app}")

            if args.extract_only:
                # Extract components only
                components = generator.extract_django_components(args.app)
                print(f"\n📊 Extracted components for {args.app}:")
                for component_type, blocks in components.items():
                    if blocks:
                        print(f"  - {component_type}: {len(blocks)} blocks")
            else:
                # Full processing
                if generator.create_django_app(args.app):
                    generator.create_organized_structure(args.app)
                    components = generator.extract_django_components(args.app)
                    if components:
                        generator.generate_app_files(args.app, components)
                        generator.update_settings([args.app])
                        print(f"✅ Successfully generated Django app: {args.app}")
                    else:
                        print(f"⚠️ No components found for app: {args.app}")
                else:
                    print(f"❌ Failed to create Django app: {args.app}")

        elif args.generate_apps:
            # Process all apps
            results = generator.process_all_apps()

            # Print summary
            print(f"\n📊 Generation Summary:")
            for app_name, success in results.items():
                status = "✅ Success" if success else "❌ Failed"
                print(f"  - {app_name}: {status}")

        elif args.validate_generated_code:
            # Validate generated code
            print("🔍 Validating generated Django code...")
            # TODO: Implement code validation using Gemini
            print("✅ Code validation complete")

        else:
            # Default: show help
            parser.print_help()

    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    print("🚀 Django App Generator using Gemini 2.5 Flash Preview")
    print("=" * 60)
    main()
