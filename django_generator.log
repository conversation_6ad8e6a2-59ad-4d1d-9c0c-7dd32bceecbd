2025-05-31 01:40:48,836 - INFO - ✅ Project structure validation passed
2025-05-31 01:40:48,836 - INFO - Django App Generator initialized with Gemini gemini-2.5-flash-preview-05-20  # @param {type: "string"}
2025-05-31 01:40:48,837 - INFO - Project root: /Users/<USER>/workspace/autoerp
2025-05-31 01:40:48,837 - INFO - Documentation directory: /Users/<USER>/workspace/autoerp/docs
2025-05-31 01:40:48,837 - INFO - Thinking budget: 8192 tokens
2025-05-31 01:40:48,837 - INFO - Dynamic thinking available: include_thoughts=True thinking_budget=None
2025-05-31 01:41:00,675 - INFO - ✅ Project structure validation passed
2025-05-31 01:41:00,675 - INFO - Django App Generator initialized with Gemini gemini-2.5-flash-preview-05-20  # @param {type: "string"}
2025-05-31 01:41:00,676 - INFO - Project root: /Users/<USER>/workspace/autoerp
2025-05-31 01:41:00,676 - INFO - Documentation directory: /Users/<USER>/workspace/autoerp/docs
2025-05-31 01:41:00,676 - INFO - Thinking budget: 8192 tokens
2025-05-31 01:41:00,676 - INFO - Dynamic thinking available: include_thoughts=True thinking_budget=None
2025-05-31 01:41:00,676 - INFO - 🔍 Discovering Django apps from documentation structure...
2025-05-31 01:41:00,685 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:41:02,928 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20%20%20#%20@param%20{type:%20%22string%22}:generateContent "HTTP/1.1 404 Not Found"
2025-05-31 01:41:02,930 - ERROR - Fatal error: 404 Not Found. {'message': '', 'status': 'Not Found'}
2025-05-31 01:41:34,040 - INFO - ✅ Project structure validation passed
2025-05-31 01:41:34,040 - INFO - Django App Generator initialized with Gemini gemini-2.5-flash-preview-05-20  # @param {type: "string"}
2025-05-31 01:41:34,040 - INFO - Project root: /Users/<USER>/workspace/autoerp
2025-05-31 01:41:34,040 - INFO - Documentation directory: /Users/<USER>/workspace/autoerp/docs
2025-05-31 01:41:34,041 - INFO - Thinking budget: 8192 tokens
2025-05-31 01:41:34,041 - INFO - Dynamic thinking available: include_thoughts=True thinking_budget=None
2025-05-31 01:41:34,041 - INFO - 🔍 Discovering Django apps from documentation structure...
2025-05-31 01:41:34,049 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:41:36,245 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20%20%20#%20@param%20{type:%20%22string%22}:generateContent "HTTP/1.1 404 Not Found"
2025-05-31 01:41:36,246 - ERROR - Fatal error: 404 Not Found. {'message': '', 'status': 'Not Found'}
2025-05-31 01:42:12,102 - INFO - ✅ Project structure validation passed
2025-05-31 01:42:12,102 - INFO - Django App Generator initialized with Gemini gemini-2.5-flash-preview-05-20
2025-05-31 01:42:12,102 - INFO - Project root: /Users/<USER>/workspace/autoerp
2025-05-31 01:42:12,102 - INFO - Documentation directory: /Users/<USER>/workspace/autoerp/docs
2025-05-31 01:42:12,102 - INFO - Thinking budget: 8192 tokens
2025-05-31 01:42:12,102 - INFO - Dynamic thinking available: include_thoughts=True thinking_budget=None
2025-05-31 01:42:12,103 - INFO - 🔍 Discovering Django apps from documentation structure...
2025-05-31 01:42:12,111 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:42:38,130 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:42:38,134 - INFO - AFC remote call 1 is done.
2025-05-31 01:42:38,134 - INFO - ✅ Discovered 0 potential Django apps: []
2025-05-31 01:42:55,537 - INFO - ✅ Project structure validation passed
2025-05-31 01:42:55,537 - INFO - Django App Generator initialized with Gemini gemini-2.5-flash-preview-05-20
2025-05-31 01:42:55,537 - INFO - Project root: /Users/<USER>/workspace/autoerp
2025-05-31 01:42:55,538 - INFO - Documentation directory: /Users/<USER>/workspace/autoerp/docs
2025-05-31 01:42:55,538 - INFO - Thinking budget: 8192 tokens
2025-05-31 01:42:55,538 - INFO - Dynamic thinking available: include_thoughts=True thinking_budget=None
2025-05-31 01:42:55,538 - INFO - 🧠 Configured for DYNAMIC thinking (unlimited budget)
2025-05-31 01:42:55,538 - INFO - 🔍 Discovering Django apps from documentation structure...
2025-05-31 01:42:55,547 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:43:22,023 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:43:22,025 - INFO - AFC remote call 1 is done.
2025-05-31 01:43:22,025 - INFO - ✅ Discovered 0 potential Django apps: []
2025-05-31 01:43:30,434 - INFO - ✅ Project structure validation passed
2025-05-31 01:43:30,435 - INFO - Django App Generator initialized with Gemini gemini-2.5-flash-preview-05-20
2025-05-31 01:43:30,435 - INFO - Project root: /Users/<USER>/workspace/autoerp
2025-05-31 01:43:30,435 - INFO - Documentation directory: /Users/<USER>/workspace/autoerp/docs
2025-05-31 01:43:30,435 - INFO - Thinking budget: 8192 tokens
2025-05-31 01:43:30,435 - INFO - Dynamic thinking available: include_thoughts=True thinking_budget=None
2025-05-31 01:43:30,435 - INFO - Processing single app: sales_distribution
2025-05-31 01:43:30,435 - INFO - 🔍 Extracting Django components for app: sales_distribution
2025-05-31 01:43:30,437 - INFO - Found 68 markdown files
2025-05-31 01:43:30,437 - INFO - Processing: sales_distribution/Dashboard.md
2025-05-31 01:43:30,439 - INFO - Using optimal config for 34275 chars: thinking_budget=24576, max_output=32768
2025-05-31 01:43:30,439 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:43:33,030 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 400 Bad Request"
2025-05-31 01:43:33,031 - ERROR - Fatal error: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': "Tool use with a response mime type: 'application/json' is unsupported", 'status': 'INVALID_ARGUMENT'}}
2025-05-31 01:44:11,794 - INFO - ✅ Project structure validation passed
2025-05-31 01:44:11,795 - INFO - Django App Generator initialized with Gemini gemini-2.5-flash-preview-05-20
2025-05-31 01:44:11,795 - INFO - Project root: /Users/<USER>/workspace/autoerp
2025-05-31 01:44:11,795 - INFO - Documentation directory: /Users/<USER>/workspace/autoerp/docs
2025-05-31 01:44:11,795 - INFO - Thinking budget: 8192 tokens
2025-05-31 01:44:11,795 - INFO - Dynamic thinking available: include_thoughts=True thinking_budget=None
2025-05-31 01:44:11,795 - INFO - Processing single app: sales_distribution
2025-05-31 01:44:11,795 - INFO - 🔍 Extracting Django components for app: sales_distribution
2025-05-31 01:44:11,797 - INFO - Found 68 markdown files
2025-05-31 01:44:11,797 - INFO - Processing: sales_distribution/Dashboard.md
2025-05-31 01:44:11,798 - INFO - Using optimal config for 34275 chars: thinking_budget=24576, max_output=32768
2025-05-31 01:44:11,798 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:44:52,360 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:44:52,363 - INFO - AFC remote call 1 is done.
2025-05-31 01:44:52,363 - INFO - Processing: sales_distribution/reports/Dashboard.md
2025-05-31 01:44:52,365 - INFO - Using optimal config for 42988 chars: thinking_budget=24576, max_output=32768
2025-05-31 01:44:52,365 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:45:33,831 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:45:33,835 - INFO - AFC remote call 1 is done.
2025-05-31 01:45:33,836 - INFO - Processing: sales_distribution/reports/CustEnquiry.md
2025-05-31 01:45:33,838 - INFO - Using optimal config for 36835 chars: thinking_budget=24576, max_output=32768
2025-05-31 01:45:33,838 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:46:35,373 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:46:35,375 - INFO - AFC remote call 1 is done.
2025-05-31 01:46:35,376 - INFO - Processing: sales_distribution/reports/CustPO.md
2025-05-31 01:46:35,377 - INFO - Using optimal config for 31664 chars: thinking_budget=24576, max_output=32768
2025-05-31 01:46:35,377 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:47:09,574 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:47:09,576 - INFO - AFC remote call 1 is done.
2025-05-31 01:47:09,577 - INFO - Processing: sales_distribution/reports/CustMaster.md
2025-05-31 01:47:09,578 - INFO - Using optimal config for 35852 chars: thinking_budget=24576, max_output=32768
2025-05-31 01:47:09,578 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:47:52,888 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:47:52,896 - INFO - AFC remote call 1 is done.
2025-05-31 01:47:52,897 - INFO - Processing: sales_distribution/reports/CustQuotation.md
2025-05-31 01:47:52,899 - INFO - Using optimal config for 33749 chars: thinking_budget=24576, max_output=32768
2025-05-31 01:47:52,899 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:48:30,263 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:48:30,267 - INFO - AFC remote call 1 is done.
2025-05-31 01:48:30,268 - INFO - Processing: sales_distribution/masters/category/SubCategoryEdit.md
2025-05-31 01:48:30,269 - INFO - Processing large file: SubCategoryEdit.md (53388 characters)
2025-05-31 01:48:30,270 - INFO - Split content into 3 intelligent chunks
2025-05-31 01:48:30,271 - INFO - Processing chunk 1/3 (1389 characters)
2025-05-31 01:48:30,271 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:49:51,159 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:49:51,160 - INFO - AFC remote call 1 is done.
2025-05-31 01:49:51,161 - ERROR - Failed to process chunk 1: Unterminated string starting at: line 20 column 16 (char 10779)
2025-05-31 01:49:51,161 - INFO - Processing chunk 2/3 (27109 characters)
2025-05-31 01:49:51,161 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:50:44,508 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:50:44,510 - INFO - AFC remote call 1 is done.
2025-05-31 01:50:44,511 - ERROR - Failed to process chunk 2: 'list' object has no attribute 'items'
2025-05-31 01:50:44,511 - INFO - Processing chunk 3/3 (24886 characters)
2025-05-31 01:50:44,511 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:51:21,065 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:51:21,068 - INFO - AFC remote call 1 is done.
2025-05-31 01:51:21,068 - ERROR - Failed to process chunk 3: 'list' object has no attribute 'items'
2025-05-31 01:51:21,068 - INFO - Large file processing complete: 0 total components
2025-05-31 01:51:21,069 - INFO - Processing: sales_distribution/masters/category/SubCategoryNew.md
2025-05-31 01:51:21,070 - INFO - Using optimal config for 45016 chars: thinking_budget=24576, max_output=32768
2025-05-31 01:51:21,070 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:52:07,144 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:52:07,146 - INFO - AFC remote call 1 is done.
2025-05-31 01:52:07,147 - INFO - Processing: sales_distribution/masters/category/CategoryEdit.md
2025-05-31 01:52:07,148 - INFO - Processing large file: CategoryEdit.md (50306 characters)
2025-05-31 01:52:07,150 - INFO - Split content into 3 intelligent chunks
2025-05-31 01:52:07,150 - INFO - Processing chunk 1/3 (1682 characters)
2025-05-31 01:52:07,150 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:52:10,523 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:52:10,524 - INFO - AFC remote call 1 is done.
2025-05-31 01:52:10,525 - INFO - Processing chunk 2/3 (29102 characters)
2025-05-31 01:52:10,525 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:52:47,898 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:52:47,901 - INFO - AFC remote call 1 is done.
2025-05-31 01:52:47,901 - ERROR - Failed to process chunk 2: 'list' object has no attribute 'items'
2025-05-31 01:52:47,901 - INFO - Processing chunk 3/3 (19518 characters)
2025-05-31 01:52:47,902 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:53:09,607 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:53:09,608 - INFO - AFC remote call 1 is done.
2025-05-31 01:53:09,608 - ERROR - Failed to process chunk 3: 'list' object has no attribute 'items'
2025-05-31 01:53:09,608 - INFO - Large file processing complete: 0 total components
2025-05-31 01:53:09,609 - INFO - Processing: sales_distribution/masters/category/SubCategoryold.md
2025-05-31 01:53:09,610 - INFO - Using optimal config for 46829 chars: thinking_budget=24576, max_output=32768
2025-05-31 01:53:09,610 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:54:02,675 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:54:02,688 - INFO - AFC remote call 1 is done.
2025-05-31 01:54:02,690 - INFO - Processing: sales_distribution/masters/category/CategoryNew.md
2025-05-31 01:54:02,691 - INFO - Using optimal config for 44174 chars: thinking_budget=24576, max_output=32768
2025-05-31 01:54:02,691 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:54:54,258 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:54:54,260 - INFO - AFC remote call 1 is done.
2025-05-31 01:54:54,261 - INFO - Processing: sales_distribution/masters/general/Product.md
2025-05-31 01:54:54,262 - INFO - Processing large file: Product.md (89084 characters)
2025-05-31 01:54:54,265 - INFO - Split content into 4 intelligent chunks
2025-05-31 01:54:54,265 - INFO - Processing chunk 1/4 (3130 characters)
2025-05-31 01:54:54,266 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:54:56,796 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:54:56,802 - INFO - AFC remote call 1 is done.
2025-05-31 01:54:56,802 - INFO - Processing chunk 2/4 (26193 characters)
2025-05-31 01:54:56,802 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:55:25,628 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:55:25,643 - INFO - AFC remote call 1 is done.
2025-05-31 01:55:25,643 - INFO - Processing chunk 3/4 (11747 characters)
2025-05-31 01:55:25,644 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:56:30,719 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:56:30,721 - INFO - AFC remote call 1 is done.
2025-05-31 01:56:30,721 - ERROR - Failed to process chunk 3: 'list' object has no attribute 'items'
2025-05-31 01:56:30,721 - INFO - Processing chunk 4/4 (48008 characters)
2025-05-31 01:56:30,721 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:57:33,590 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:57:33,591 - INFO - AFC remote call 1 is done.
2025-05-31 01:57:33,591 - ERROR - Failed to process chunk 4: Unterminated string starting at: line 59 column 13 (char 2707)
2025-05-31 01:57:33,591 - INFO - Large file processing complete: 0 total components
2025-05-31 01:57:33,592 - INFO - Processing: sales_distribution/masters/general/Dashboard.md
2025-05-31 01:57:33,593 - INFO - Using optimal config for 37184 chars: thinking_budget=24576, max_output=32768
2025-05-31 01:57:33,593 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:58:13,833 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 01:58:13,836 - INFO - AFC remote call 1 is done.
2025-05-31 01:58:13,837 - INFO - Processing: sales_distribution/masters/work_order_management/WO_SubCategory_Dashboard.md
2025-05-31 01:58:13,839 - INFO - Using optimal config for 41134 chars: thinking_budget=24576, max_output=32768
2025-05-31 01:58:13,839 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 01:58:14,242 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 503 Service Unavailable"
2025-05-31 01:58:14,243 - ERROR - Fatal error: 503 UNAVAILABLE. {'error': {'code': 503, 'message': 'The service is currently unavailable.', 'status': 'UNAVAILABLE'}}
2025-05-31 12:09:20,843 - INFO - ✅ Project structure validation passed
2025-05-31 12:09:20,844 - INFO - Django App Generator initialized with Gemini gemini-2.5-flash-preview-05-20
2025-05-31 12:09:20,844 - INFO - Project root: /Users/<USER>/workspace/autoerp
2025-05-31 12:09:20,844 - INFO - Documentation directory: /Users/<USER>/workspace/autoerp/docs
2025-05-31 12:09:20,844 - INFO - Thinking budget: 8192 tokens
2025-05-31 12:09:20,844 - INFO - Dynamic thinking available: include_thoughts=True thinking_budget=None
2025-05-31 12:09:20,844 - INFO - Processing single app: sales_distribution
2025-05-31 12:09:22,236 - INFO - Successfully created Django app: sales_distribution
2025-05-31 12:09:22,237 - ERROR - Fatal error: unsupported operand type(s) for /: 'str' and 'str'
2025-05-31 12:10:25,207 - INFO - ✅ Project structure validation passed
2025-05-31 12:10:25,208 - INFO - Django App Generator initialized with Gemini gemini-2.5-flash-preview-05-20
2025-05-31 12:10:25,208 - INFO - Project root: /Users/<USER>/workspace/autoerp
2025-05-31 12:10:25,208 - INFO - Documentation directory: /Users/<USER>/workspace/autoerp/docs
2025-05-31 12:10:25,208 - INFO - Thinking budget: 8192 tokens
2025-05-31 12:10:25,208 - INFO - Dynamic thinking available: include_thoughts=True thinking_budget=None
2025-05-31 12:10:25,209 - INFO - 🧠 Configured for DYNAMIC thinking (unlimited budget)
2025-05-31 12:10:25,209 - INFO - Processing single app: sales_distribution
2025-05-31 12:10:25,209 - WARNING - App directory sales_distribution already exists, skipping creation
2025-05-31 12:10:25,213 - INFO - Created organized structure for app: sales_distribution
2025-05-31 12:10:25,213 - INFO - 🔍 Extracting Django components for app: sales_distribution
2025-05-31 12:10:25,220 - INFO - Found 68 markdown files
2025-05-31 12:10:25,221 - INFO - Processing: sales_distribution/Dashboard.md
2025-05-31 12:10:25,222 - INFO - Using cached result for Dashboard.md
2025-05-31 12:10:25,223 - INFO - Processing: sales_distribution/reports/Dashboard.md
2025-05-31 12:10:25,224 - INFO - Using cached result for Dashboard.md
2025-05-31 12:10:25,225 - INFO - Processing: sales_distribution/reports/CustEnquiry.md
2025-05-31 12:10:25,227 - INFO - Using cached result for CustEnquiry.md
2025-05-31 12:10:25,228 - INFO - Processing: sales_distribution/reports/CustPO.md
2025-05-31 12:10:25,231 - INFO - Using cached result for CustPO.md
2025-05-31 12:10:25,232 - INFO - Processing: sales_distribution/reports/CustMaster.md
2025-05-31 12:10:25,234 - INFO - Using cached result for CustMaster.md
2025-05-31 12:10:25,234 - INFO - Processing: sales_distribution/reports/CustQuotation.md
2025-05-31 12:10:25,235 - INFO - Using cached result for CustQuotation.md
2025-05-31 12:10:25,235 - INFO - Processing: sales_distribution/masters/category/SubCategoryEdit.md
2025-05-31 12:10:25,238 - INFO - Using cached result for SubCategoryEdit.md
2025-05-31 12:10:25,238 - INFO - Processing: sales_distribution/masters/category/SubCategoryNew.md
2025-05-31 12:10:25,241 - INFO - Using cached result for SubCategoryNew.md
2025-05-31 12:10:25,241 - INFO - Processing: sales_distribution/masters/category/CategoryEdit.md
2025-05-31 12:10:25,243 - INFO - Using cached result for CategoryEdit.md
2025-05-31 12:10:25,243 - INFO - Processing: sales_distribution/masters/category/SubCategoryold.md
2025-05-31 12:10:25,245 - INFO - Using cached result for SubCategoryold.md
2025-05-31 12:10:25,246 - INFO - Processing: sales_distribution/masters/category/CategoryNew.md
2025-05-31 12:10:25,249 - INFO - Using cached result for CategoryNew.md
2025-05-31 12:10:25,250 - INFO - Processing: sales_distribution/masters/general/Product.md
2025-05-31 12:10:25,252 - INFO - Using cached result for Product.md
2025-05-31 12:10:25,253 - INFO - Processing: sales_distribution/masters/general/Dashboard.md
2025-05-31 12:10:25,255 - INFO - Using cached result for Dashboard.md
2025-05-31 12:10:25,255 - INFO - Processing: sales_distribution/masters/work_order_management/WO_SubCategory_Dashboard.md
2025-05-31 12:10:25,258 - INFO - Using optimal config for 41134 chars: thinking_budget=24576, max_output=32768
2025-05-31 12:10:25,258 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 12:11:07,995 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 12:11:07,998 - INFO - AFC remote call 1 is done.
2025-05-31 12:11:07,999 - INFO - ✅ Successfully extracted components from WO_SubCategory_Dashboard.md
2025-05-31 12:11:08,000 - INFO - Processing: sales_distribution/masters/work_order_management/WO_Category_Dashboard.md
2025-05-31 12:11:08,001 - INFO - Using optimal config for 46433 chars: thinking_budget=24576, max_output=32768
2025-05-31 12:11:08,001 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 12:11:48,280 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 12:11:48,286 - INFO - AFC remote call 1 is done.
2025-05-31 12:11:48,287 - INFO - ✅ Successfully extracted components from WO_Category_Dashboard.md
2025-05-31 12:11:48,288 - INFO - Processing: sales_distribution/masters/work_order_management/WO_Release_DA.md
2025-05-31 12:11:48,289 - INFO - Using optimal config for 38349 chars: thinking_budget=24576, max_output=32768
2025-05-31 12:11:48,289 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 12:12:27,311 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 12:12:27,313 - INFO - AFC remote call 1 is done.
2025-05-31 12:12:27,314 - INFO - ✅ Successfully extracted components from WO_Release_DA.md
2025-05-31 12:12:27,315 - INFO - Processing: sales_distribution/masters/work_order_management/WOTypes.md
2025-05-31 12:12:27,317 - INFO - Using optimal config for 40885 chars: thinking_budget=24576, max_output=32768
2025-05-31 12:12:27,317 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 12:13:06,527 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 12:13:06,530 - INFO - AFC remote call 1 is done.
2025-05-31 12:13:06,531 - INFO - ✅ Successfully extracted components from WOTypes.md
2025-05-31 12:13:06,532 - INFO - Processing: sales_distribution/masters/customer/CustomerMaster_New.md
2025-05-31 12:13:06,533 - INFO - Processing large file: CustomerMaster_New.md (85488 characters)
2025-05-31 12:13:06,536 - INFO - Split content into 5 intelligent chunks
2025-05-31 12:13:06,537 - INFO - Processing chunk 1/5 (6138 characters)
2025-05-31 12:13:06,537 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 12:14:18,212 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 12:14:18,214 - INFO - AFC remote call 1 is done.
2025-05-31 12:14:18,215 - ERROR - Failed to process chunk 1: Invalid \escape: line 12 column 3465 (char 11637)
2025-05-31 12:14:18,215 - INFO - Processing chunk 2/5 (27364 characters)
2025-05-31 12:14:18,216 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 12:15:29,385 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 12:15:29,387 - INFO - AFC remote call 1 is done.
2025-05-31 12:15:29,387 - ERROR - Failed to process chunk 2: the JSON object must be str, bytes or bytearray, not NoneType
2025-05-31 12:15:29,388 - INFO - Processing chunk 3/5 (27950 characters)
2025-05-31 12:15:29,388 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 12:16:14,119 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 12:16:14,121 - INFO - AFC remote call 1 is done.
2025-05-31 12:16:14,122 - ERROR - Failed to process chunk 3: 'list' object has no attribute 'items'
2025-05-31 12:16:14,122 - INFO - Processing chunk 4/5 (20994 characters)
2025-05-31 12:16:14,122 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 12:16:55,488 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 12:16:55,490 - INFO - AFC remote call 1 is done.
2025-05-31 12:16:55,490 - INFO - Processing chunk 5/5 (3034 characters)
2025-05-31 12:16:55,490 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 12:17:43,893 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 12:17:43,908 - INFO - AFC remote call 1 is done.
2025-05-31 12:17:43,910 - INFO - Large file processing complete: 0 total components
2025-05-31 12:17:43,911 - INFO - Processing: sales_distribution/masters/customer/CustomerMaster_Print.md
2025-05-31 12:17:43,912 - INFO - Processing large file: CustomerMaster_Print.md (55168 characters)
2025-05-31 12:17:43,914 - INFO - Split content into 3 intelligent chunks
2025-05-31 12:17:43,914 - INFO - Processing chunk 1/3 (1421 characters)
2025-05-31 12:17:43,914 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 12:17:47,113 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 12:17:47,115 - INFO - AFC remote call 1 is done.
2025-05-31 12:17:47,115 - INFO - Processing chunk 2/3 (29130 characters)
2025-05-31 12:17:47,115 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 12:18:52,964 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 12:18:52,967 - INFO - AFC remote call 1 is done.
2025-05-31 12:18:52,967 - ERROR - Failed to process chunk 2: 'list' object has no attribute 'items'
2025-05-31 12:18:52,968 - INFO - Processing chunk 3/3 (24613 characters)
2025-05-31 12:18:52,968 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 12:19:26,959 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 12:19:26,961 - INFO - AFC remote call 1 is done.
2025-05-31 12:19:26,961 - ERROR - Failed to process chunk 3: 'list' object has no attribute 'items'
2025-05-31 12:19:26,962 - INFO - Large file processing complete: 0 total components
2025-05-31 12:19:26,962 - INFO - Processing: sales_distribution/masters/customer/CustomerMaster_Edit_Details.md
2025-05-31 12:19:26,963 - INFO - Processing large file: CustomerMaster_Edit_Details.md (63993 characters)
2025-05-31 12:19:26,965 - INFO - Split content into 4 intelligent chunks
2025-05-31 12:19:26,965 - INFO - Processing chunk 1/4 (1448 characters)
2025-05-31 12:19:26,965 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 12:19:29,937 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 12:19:29,938 - INFO - AFC remote call 1 is done.
2025-05-31 12:19:29,939 - INFO - Processing chunk 2/4 (27204 characters)
2025-05-31 12:19:29,939 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 12:20:48,366 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 12:20:48,369 - INFO - AFC remote call 1 is done.
2025-05-31 12:20:48,369 - ERROR - Failed to process chunk 2: Unterminated string starting at: line 63 column 15 (char 41684)
2025-05-31 12:20:48,369 - INFO - Processing chunk 3/4 (24265 characters)
2025-05-31 12:20:48,370 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 12:21:29,427 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 12:21:29,429 - INFO - AFC remote call 1 is done.
2025-05-31 12:21:29,429 - ERROR - Failed to process chunk 3: 'list' object has no attribute 'items'
2025-05-31 12:21:29,429 - INFO - Processing chunk 4/4 (11071 characters)
2025-05-31 12:21:29,430 - INFO - AFC is enabled with max remote calls: 10.
2025-05-31 12:22:23,288 - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent "HTTP/1.1 200 OK"
2025-05-31 12:22:23,290 - INFO - AFC remote call 1 is done.
2025-05-31 12:22:23,290 - INFO - Large file processing complete: 0 total components
2025-05-31 12:22:23,291 - INFO - Processing: sales_distribution/masters/customer/CustomerMaster_Print_Details.md
2025-05-31 12:22:23,292 - INFO - Processing large file: CustomerMaster_Print_Details.md (62882 characters)
2025-05-31 12:22:23,294 - INFO - Split content into 3 intelligent chunks
2025-05-31 12:22:23,294 - INFO - Processing chunk 1/3 (28024 characters)
2025-05-31 12:22:23,294 - INFO - AFC is enabled with max remote calls: 10.
