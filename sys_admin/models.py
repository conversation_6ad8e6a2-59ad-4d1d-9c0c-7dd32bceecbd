from django.db import models

# Create your models here.

from django.db import models


class Country(models.Model):
    # CId is the primary key in the existing database
    id = models.IntegerField(db_column="CId", primary_key=True)
    country_name = models.CharField(
        db_column="CountryName", max_length=255, blank=False, null=False
    )
    currency = models.CharField(
        db_column="Currency", max_length=50, blank=False, null=False
    )
    symbol = models.CharField(
        db_column="Symbol", max_length=10, blank=False, null=False
    )

    class Meta:
        managed = False  # Important: Tells Django not to manage this table (it already exists)
        db_table = "tblCountry"  # Maps to the existing database table name
        verbose_name = "Country"
        verbose_name_plural = "Countries"
        ordering = ["country_name"]  # Default ordering

    def __str__(self):
        return self.country_name

    # Example of a 'fat model' business logic method (if identified in original ASP.NET)
    # No complex logic identified in this simple ASP.NET example, but it would go here.
    # def get_full_country_info(self):
    #     return f"{self.country_name} ({self.currency} {self.symbol})"
